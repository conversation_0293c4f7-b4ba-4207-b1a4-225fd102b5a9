#!/usr/bin/env python3
"""
检查Python文件语法
"""

import ast
import sys

def check_syntax(file_path):
    """检查文件语法"""
    print(f"检查文件语法: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✅ {file_path} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path} 语法错误:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ {file_path} 检查失败: {e}")
        return False

def main():
    """主函数"""
    files_to_check = [
        'table_render/main_generator.py',
        'table_render/utils/parallel_generator.py',
        'table_render/config.py'
    ]
    
    all_good = True
    for file_path in files_to_check:
        if not check_syntax(file_path):
            all_good = False
    
    if all_good:
        print("\n🎉 所有文件语法检查通过！")
    else:
        print("\n❌ 存在语法错误！")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
