#!/usr/bin/env python3
"""
TurboJPEG保存器使用示例

展示如何在TableRender项目中使用TurboJPEG保存器进行性能优化。
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
from table_render.utils.file_utils import FileUtils
from PIL import Image
import numpy as np


def create_sample_table_image():
    """创建一个模拟的表格图像"""
    width, height = 1000, 600
    
    # 创建白色背景
    image_array = np.full((height, width, 3), 255, dtype=np.uint8)
    
    # 绘制表格边框
    # 外边框
    image_array[0:3, :] = [0, 0, 0]  # 顶边
    image_array[-3:, :] = [0, 0, 0]  # 底边
    image_array[:, 0:3] = [0, 0, 0]  # 左边
    image_array[:, -3:] = [0, 0, 0]  # 右边
    
    # 内部网格线
    rows, cols = 6, 5
    row_height = height // rows
    col_width = width // cols
    
    # 水平线
    for i in range(1, rows):
        y = i * row_height
        image_array[y:y+2, :] = [0, 0, 0]
    
    # 垂直线
    for j in range(1, cols):
        x = j * col_width
        image_array[:, x:x+2] = [0, 0, 0]
    
    # 填充表头（第一行）
    header_color = [200, 220, 255]  # 浅蓝色
    image_array[3:row_height-3, 3:-3] = header_color
    
    # 填充一些数据单元格
    colors = [
        [255, 240, 240],  # 浅红
        [240, 255, 240],  # 浅绿
        [255, 255, 240],  # 浅黄
        [240, 240, 255],  # 浅蓝
    ]
    
    for i in range(1, rows):
        for j in range(cols):
            if np.random.random() > 0.3:  # 70%的单元格有背景色
                color = colors[np.random.randint(0, len(colors))]
                y_start = i * row_height + 3
                y_end = (i + 1) * row_height - 3
                x_start = j * col_width + 3
                x_end = (j + 1) * col_width - 3
                image_array[y_start:y_end, x_start:x_end] = color
    
    return Image.fromarray(image_array)


def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("=== TurboJPEG保存器基本使用演示 ===")
    
    # 创建测试图像
    print("1. 创建测试表格图像...")
    table_image = create_sample_table_image()
    print(f"   图像尺寸: {table_image.size}")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 方法1: 直接使用TurboJPEGSaver
        print("\n2. 直接使用TurboJPEGSaver...")
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        print(f"   TurboJPEG可用: {saver.turbo_available}")
        
        # 保存为JPEG
        jpeg_path = temp_path / "table_direct.jpg"
        start_time = time.time()
        success = saver.save_image(table_image, jpeg_path, format_hint="jpeg")
        jpeg_time = time.time() - start_time
        print(f"   JPEG保存: {'成功' if success else '失败'}, 耗时: {jpeg_time*1000:.2f}ms")
        
        # 保存为PNG
        png_path = temp_path / "table_direct.png"
        start_time = time.time()
        success = saver.save_image(table_image, png_path, format_hint="png")
        png_time = time.time() - start_time
        print(f"   PNG保存: {'成功' if success else '失败'}, 耗时: {png_time*1000:.2f}ms")
        
        # 方法2: 通过FileUtils使用
        print("\n3. 通过FileUtils使用...")
        
        # 转换为字节数据（模拟现有流程）
        import io
        buffer = io.BytesIO()
        table_image.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()
        print(f"   图像字节大小: {len(image_bytes)} bytes")
        
        # 原始方法
        original_path = temp_path / "table_original.png"
        start_time = time.time()
        FileUtils.save_image(image_bytes, str(original_path))
        original_time = time.time() - start_time
        print(f"   原始方法: 耗时: {original_time*1000:.2f}ms")
        
        # 优化方法（PNG）
        optimized_png_path = temp_path / "table_optimized.png"
        start_time = time.time()
        FileUtils.save_image_optimized(image_bytes, str(optimized_png_path), 
                                     use_turbo=True, format_hint="png")
        optimized_png_time = time.time() - start_time
        print(f"   优化方法(PNG): 耗时: {optimized_png_time*1000:.2f}ms")
        
        # 优化方法（JPEG）
        optimized_jpeg_path = temp_path / "table_optimized.jpg"
        start_time = time.time()
        FileUtils.save_image_optimized(image_bytes, str(optimized_jpeg_path), 
                                     use_turbo=True, format_hint="jpeg")
        optimized_jpeg_time = time.time() - start_time
        print(f"   优化方法(JPEG): 耗时: {optimized_jpeg_time*1000:.2f}ms")
        
        # 性能对比
        print("\n4. 性能对比:")
        if saver.turbo_available and optimized_jpeg_time > 0:
            speedup = original_time / optimized_jpeg_time
            print(f"   JPEG加速比: {speedup:.2f}x")
            print(f"   节省时间: {(original_time - optimized_jpeg_time)*1000:.2f}ms")
        
        # 文件大小对比
        print("\n5. 文件大小对比:")
        if original_path.exists():
            print(f"   原始PNG: {original_path.stat().st_size / 1024:.2f} KB")
        if optimized_jpeg_path.exists():
            print(f"   优化JPEG: {optimized_jpeg_path.stat().st_size / 1024:.2f} KB")
            if original_path.exists():
                compression = original_path.stat().st_size / optimized_jpeg_path.stat().st_size
                print(f"   压缩比: {compression:.2f}x")


def demonstrate_save_sample_integration():
    """演示save_sample集成"""
    print("\n=== save_sample集成演示 ===")
    
    # 创建测试数据
    table_image = create_sample_table_image()
    
    # 转换为字节数据
    import io
    buffer = io.BytesIO()
    table_image.save(buffer, format='PNG')
    image_bytes = buffer.getvalue()
    
    # 创建测试标注和元数据
    test_annotations = {
        "image_filename": "000000.png",
        "cells": [
            {
                "id": "cell_0_0",
                "polygon": [[10, 10], [200, 10], [200, 100], [10, 100]],
                "text": "Header 1"
            },
            {
                "id": "cell_0_1", 
                "polygon": [[210, 10], [400, 10], [400, 100], [210, 100]],
                "text": "Header 2"
            },
            {
                "id": "cell_1_0",
                "polygon": [[10, 110], [200, 110], [200, 200], [10, 200]],
                "text": "Data 1"
            }
        ]
    }
    
    test_metadata = {
        "sample_index": 0,
        "generation_time": "2025-01-01T00:00:00",
        "image_size": table_image.size,
        "cell_count": len(test_annotations["cells"])
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建输出目录结构
        output_dirs = FileUtils.ensure_output_dirs(str(temp_path / "output"))
        print(f"输出目录: {output_dirs}")
        
        # 测试原始save_sample
        print("\n1. 原始save_sample测试...")
        start_time = time.time()
        FileUtils.save_sample(
            sample_index=0,
            image_bytes=image_bytes,
            annotations=test_annotations,
            metadata=test_metadata,
            output_dirs=output_dirs
        )
        original_time = time.time() - start_time
        print(f"   原始方法耗时: {original_time*1000:.2f}ms")
        
        # 测试优化save_sample
        print("\n2. 优化save_sample测试...")
        start_time = time.time()
        FileUtils.save_sample(
            sample_index=1,
            image_bytes=image_bytes,
            annotations=test_annotations,
            metadata=test_metadata,
            output_dirs=output_dirs,
            use_turbo=True,
            format_hint="jpeg"
        )
        optimized_time = time.time() - start_time
        print(f"   优化方法耗时: {optimized_time*1000:.2f}ms")
        
        # 检查生成的文件
        print("\n3. 生成文件检查:")
        images_dir = Path(output_dirs['images'])
        annotations_dir = Path(output_dirs['annotations'])
        metadata_dir = Path(output_dirs['metadata'])
        
        original_image = images_dir / "000000.png"
        optimized_image = images_dir / "000001.png"
        
        print(f"   原始图像: {original_image.exists()}, 大小: {original_image.stat().st_size if original_image.exists() else 0} bytes")
        print(f"   优化图像: {optimized_image.exists()}, 大小: {optimized_image.stat().st_size if optimized_image.exists() else 0} bytes")
        print(f"   标注文件: {len(list(annotations_dir.glob('*.json')))} 个")
        print(f"   元数据文件: {len(list(metadata_dir.glob('*.json')))} 个")
        
        # 性能对比
        if optimized_time > 0:
            speedup = original_time / optimized_time
            print(f"\n4. 性能提升: {speedup:.2f}x")
            print(f"   节省时间: {(original_time - optimized_time)*1000:.2f}ms")


def main():
    """主函数"""
    print("TableRender TurboJPEG保存器使用示例")
    print("=" * 50)
    
    # 基本使用演示
    demonstrate_basic_usage()
    
    # save_sample集成演示
    demonstrate_save_sample_integration()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n使用建议:")
    print("1. 对于速度优先的场景，使用format_hint='jpeg'")
    print("2. 对于质量优先的场景，保持使用PNG格式")
    print("3. 在MainGenerator中启用use_turbo=True来获得性能提升")
    print("4. TurboJPEG不可用时会自动降级，不影响功能")


if __name__ == "__main__":
    main()
