"""
性能分析工具

用于详细监控TableRender各个阶段的性能指标
"""

import time
import psutil
import logging
import json
import os
from contextlib import contextmanager
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class PerformanceMetric:
    """单个性能指标"""
    stage_name: str
    start_time: float
    end_time: float
    duration: float
    memory_before: float
    memory_after: float
    memory_delta: float
    additional_info: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['duration_ms'] = self.duration * 1000  # 转换为毫秒
        result['memory_before_mb'] = self.memory_before
        result['memory_after_mb'] = self.memory_after
        result['memory_delta_mb'] = self.memory_delta
        return result


class PerformanceProfiler:
    """
    性能分析器
    
    用于监控TableRender各个阶段的详细性能数据
    """
    
    def __init__(self, output_dir: str = None):
        """
        初始化性能分析器
        
        Args:
            output_dir: 性能数据输出目录
        """
        self.logger = logging.getLogger(__name__)
        self.process = psutil.Process()
        self.metrics: List[PerformanceMetric] = []
        self.current_sample_metrics: List[PerformanceMetric] = []
        self.sample_start_time = None
        self.output_dir = output_dir or "./performance_logs"
        
        # 确保输出目录存在
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        # 初始化性能日志文件
        self.perf_log_file = os.path.join(self.output_dir, "performance_detailed.log")
        self.perf_json_file = os.path.join(self.output_dir, "performance_data.json")
        
        # 设置性能专用logger
        self.perf_logger = logging.getLogger("performance")
        self.perf_logger.setLevel(logging.INFO)
        
        # 创建文件handler
        if not self.perf_logger.handlers:
            handler = logging.FileHandler(self.perf_log_file, mode='w', encoding='utf-8')
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S.%f'
            )
            handler.setFormatter(formatter)
            self.perf_logger.addHandler(handler)
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / 1024 / 1024
        except Exception as e:
            self.logger.warning(f"获取内存使用量失败: {e}")
            return 0.0
    
    @contextmanager
    def profile_stage(self, stage_name: str, additional_info: Dict[str, Any] = None):
        """
        性能监控上下文管理器
        
        Args:
            stage_name: 阶段名称
            additional_info: 额外信息
        """
        start_time = time.time()
        memory_before = self.get_memory_usage()
        
        self.perf_logger.info(f"[START] {stage_name} - Memory: {memory_before:.2f}MB")
        
        try:
            yield
        finally:
            end_time = time.time()
            memory_after = self.get_memory_usage()
            duration = end_time - start_time
            memory_delta = memory_after - memory_before
            
            # 创建性能指标
            metric = PerformanceMetric(
                stage_name=stage_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_delta=memory_delta,
                additional_info=additional_info or {}
            )
            
            # 记录指标
            self.metrics.append(metric)
            self.current_sample_metrics.append(metric)
            
            # 记录详细日志
            self.perf_logger.info(
                f"[END] {stage_name} - Duration: {duration*1000:.2f}ms, "
                f"Memory: {memory_before:.2f}MB -> {memory_after:.2f}MB "
                f"(Δ{memory_delta:+.2f}MB)"
            )
            
            # 如果耗时超过100ms，记录警告
            if duration > 0.1:
                self.perf_logger.warning(
                    f"[SLOW] {stage_name} took {duration*1000:.2f}ms"
                )
    
    def start_sample(self, sample_index: int):
        """开始监控新样本"""
        self.sample_start_time = time.time()
        self.current_sample_metrics = []
        self.perf_logger.info(f"=== 开始样本 {sample_index} ===")
    
    def end_sample(self, sample_index: int):
        """结束样本监控"""
        if self.sample_start_time is None:
            return
            
        total_duration = time.time() - self.sample_start_time
        
        # 计算各阶段耗时占比
        stage_durations = {}
        for metric in self.current_sample_metrics:
            stage_durations[metric.stage_name] = metric.duration
        
        total_measured = sum(stage_durations.values())
        
        self.perf_logger.info(f"=== 样本 {sample_index} 完成 ===")
        self.perf_logger.info(f"总耗时: {total_duration*1000:.2f}ms")
        self.perf_logger.info(f"已测量耗时: {total_measured*1000:.2f}ms")
        self.perf_logger.info(f"未测量耗时: {(total_duration-total_measured)*1000:.2f}ms")
        
        # 记录各阶段耗时占比
        for stage_name, duration in sorted(stage_durations.items(), key=lambda x: x[1], reverse=True):
            percentage = (duration / total_duration) * 100
            self.perf_logger.info(f"  {stage_name}: {duration*1000:.2f}ms ({percentage:.1f}%)")
        
        self.sample_start_time = None
    
    def save_performance_data(self):
        """保存性能数据到JSON文件"""
        try:
            # 准备数据
            data = {
                'total_samples': len([m for m in self.metrics if m.stage_name == 'sample_total']),
                'total_metrics': len(self.metrics),
                'metrics': [metric.to_dict() for metric in self.metrics]
            }
            
            # 计算统计信息
            stage_stats = {}
            for metric in self.metrics:
                stage_name = metric.stage_name
                if stage_name not in stage_stats:
                    stage_stats[stage_name] = {
                        'count': 0,
                        'total_duration': 0,
                        'total_memory_delta': 0,
                        'max_duration': 0,
                        'min_duration': float('inf')
                    }
                
                stats = stage_stats[stage_name]
                stats['count'] += 1
                stats['total_duration'] += metric.duration
                stats['total_memory_delta'] += metric.memory_delta
                stats['max_duration'] = max(stats['max_duration'], metric.duration)
                stats['min_duration'] = min(stats['min_duration'], metric.duration)
            
            # 计算平均值
            for stage_name, stats in stage_stats.items():
                stats['avg_duration'] = stats['total_duration'] / stats['count']
                stats['avg_memory_delta'] = stats['total_memory_delta'] / stats['count']
                stats['avg_duration_ms'] = stats['avg_duration'] * 1000
                if stats['min_duration'] == float('inf'):
                    stats['min_duration'] = 0
            
            data['stage_statistics'] = stage_stats
            
            # 保存到文件
            with open(self.perf_json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能数据已保存到: {self.perf_json_file}")
            
        except Exception as e:
            self.logger.error(f"保存性能数据失败: {e}")
    
    def print_summary(self):
        """打印性能摘要"""
        if not self.metrics:
            print("没有性能数据")
            return
        
        print("\n" + "="*60)
        print("性能分析摘要")
        print("="*60)
        
        # 按阶段统计
        stage_stats = {}
        for metric in self.metrics:
            stage_name = metric.stage_name
            if stage_name not in stage_stats:
                stage_stats[stage_name] = []
            stage_stats[stage_name].append(metric.duration)
        
        # 打印各阶段统计
        for stage_name, durations in sorted(stage_stats.items()):
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            print(f"{stage_name}:")
            print(f"  调用次数: {len(durations)}")
            print(f"  平均耗时: {avg_duration*1000:.2f}ms")
            print(f"  最大耗时: {max_duration*1000:.2f}ms")
            print(f"  最小耗时: {min_duration*1000:.2f}ms")
            print(f"  总耗时: {sum(durations)*1000:.2f}ms")
            print()


# 全局性能分析器实例
_global_profiler: Optional[PerformanceProfiler] = None


def get_profiler() -> Optional[PerformanceProfiler]:
    """获取全局性能分析器"""
    return _global_profiler


def init_profiler(output_dir: str = None) -> PerformanceProfiler:
    """初始化全局性能分析器"""
    global _global_profiler
    _global_profiler = PerformanceProfiler(output_dir)
    return _global_profiler


def profile_stage(stage_name: str, additional_info: Dict[str, Any] = None):
    """性能监控装饰器/上下文管理器"""
    profiler = get_profiler()
    if profiler:
        return profiler.profile_stage(stage_name, additional_info)
    else:
        # 如果没有初始化profiler，返回一个空的上下文管理器
        from contextlib import nullcontext
        return nullcontext()
