"""
TurboJPEG图像保存器

实现TurboJPEG高速图像保存，相比PIL提升3-5倍保存速度。
支持优雅降级，当TurboJPEG不可用时自动使用PIL备选方案。
"""

import io
import logging
import numpy as np
from pathlib import Path
from typing import Union, Optional
from PIL import Image


class TurboJPEGSaver:
    """
    TurboJPEG高速图像保存器
    
    使用PyTurboJPEG库实现高速JPEG编码，相比PIL提升3-5倍保存速度。
    支持优雅降级，当TurboJPEG不可用时自动使用PIL备选方案。
    """
    
    def __init__(self, quality: int = 95, enable_turbo: bool = True):
        """
        初始化TurboJPEG保存器
        
        Args:
            quality: JPEG质量参数 (1-100)
            enable_turbo: 是否启用TurboJPEG加速
        """
        self.quality = max(1, min(100, quality))
        self.enable_turbo = enable_turbo
        self.logger = logging.getLogger(__name__)
        
        # 尝试导入TurboJPEG
        self.turbo_jpeg = None
        self.turbo_available = False
        
        if enable_turbo:
            try:
                from turbojpeg import TurboJPEG
                self.turbo_jpeg = TurboJPEG()
                self.turbo_available = True
                self.logger.info("TurboJPEG加速已启用")
            except ImportError:
                self.logger.warning("PyTurboJPEG未安装，将使用PIL备选方案")
            except Exception as e:
                self.logger.warning(f"TurboJPEG初始化失败: {e}，将使用PIL备选方案")
    
    def save_image_bytes(self, image_bytes: bytes, 
                        file_path: Union[str, Path],
                        format_hint: Optional[str] = None) -> bool:
        """
        从字节数据保存图像文件（兼容现有接口）
        
        Args:
            image_bytes: 图像的二进制数据
            file_path: 保存路径
            format_hint: 格式提示 ('jpeg', 'png', 'auto')
            
        Returns:
            保存是否成功
        """
        try:
            file_path = Path(file_path)
            
            # 确保输出目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 智能格式选择
            target_format = self._determine_format(file_path, format_hint)
            
            if target_format.lower() == 'jpeg' and self.turbo_available:
                # 加载为PIL图像然后用TurboJPEG保存
                image = Image.open(io.BytesIO(image_bytes))
                return self._save_with_turbojpeg(image, file_path)
            else:
                # 直接写入字节数据（保持原有行为）
                with open(file_path, 'wb') as f:
                    f.write(image_bytes)
                return True
                
        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            return False
    
    def save_image(self, image: Union[Image.Image, np.ndarray], 
                   file_path: Union[str, Path], 
                   format_hint: Optional[str] = None) -> bool:
        """
        保存图像文件
        
        Args:
            image: PIL图像对象或numpy数组
            file_path: 保存路径
            format_hint: 格式提示 ('jpeg', 'png', 'auto')
            
        Returns:
            保存是否成功
        """
        try:
            file_path = Path(file_path)
            
            # 确保输出目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 智能格式选择
            target_format = self._determine_format(file_path, format_hint)
            
            # 转换为PIL Image（如果需要）
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            
            # 根据格式选择保存方法
            if target_format.lower() == 'jpeg' and self.turbo_available:
                return self._save_with_turbojpeg(image, file_path)
            else:
                return self._save_with_pil(image, file_path, target_format)
                
        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            return False
    
    def _determine_format(self, file_path: Path, format_hint: Optional[str]) -> str:
        """
        智能格式选择
        
        Args:
            file_path: 文件路径
            format_hint: 格式提示
            
        Returns:
            目标格式 ('jpeg' 或 'png')
        """
        if format_hint:
            if format_hint.lower() in ['jpeg', 'jpg']:
                return 'jpeg'
            elif format_hint.lower() == 'png':
                return 'png'
        
        # 根据文件扩展名判断
        suffix = file_path.suffix.lower()
        if suffix in ['.jpg', '.jpeg']:
            return 'jpeg'
        elif suffix == '.png':
            return 'png'
        
        # 默认使用PNG（保持原有行为）
        return 'png'
    
    def _save_with_turbojpeg(self, image: Image.Image, file_path: Path) -> bool:
        """
        使用TurboJPEG保存图像
        
        Args:
            image: PIL图像对象
            file_path: 保存路径
            
        Returns:
            保存是否成功
        """
        try:
            # 确保图像是RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为numpy数组
            image_array = np.array(image)
            
            # 使用TurboJPEG编码
            jpeg_data = self.turbo_jpeg.encode(image_array, quality=self.quality)
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(jpeg_data)
            
            return True
            
        except Exception as e:
            self.logger.warning(f"TurboJPEG保存失败，回退到PIL: {e}")
            return self._save_with_pil(image, file_path, 'jpeg')
    
    def _save_with_pil(self, image: Image.Image, file_path: Path, format_type: str) -> bool:
        """
        使用PIL保存图像（备选方案）
        
        Args:
            image: PIL图像对象
            file_path: 保存路径
            format_type: 格式类型
            
        Returns:
            保存是否成功
        """
        try:
            if format_type.lower() == 'jpeg':
                # 确保图像是RGB模式
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # 优化的JPEG保存参数
                image.save(
                    file_path,
                    format='JPEG',
                    quality=self.quality,
                    optimize=True,
                    progressive=False  # 禁用渐进式以提升速度
                )
            else:
                # PNG保存
                image.save(file_path, format='PNG', optimize=True)
            
            return True
            
        except Exception as e:
            self.logger.error(f"PIL保存失败: {e}")
            return False
    
    def get_stats(self) -> dict:
        """
        获取保存器统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'turbo_available': self.turbo_available,
            'quality': self.quality,
            'enable_turbo': self.enable_turbo
        }
