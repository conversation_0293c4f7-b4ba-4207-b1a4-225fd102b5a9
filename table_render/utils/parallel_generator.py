"""
并行生成器

实现样本级多线程并行生成，提升TableRender的生成效率。
支持线程安全的样本生成和TurboJPEG优化。
"""

import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable

from ..main_generator import MainGenerator
from .turbo_jpeg_saver import TurboJPEGSaver


class ParallelGeneratorConfig:
    """并行生成器配置类"""
    
    def __init__(self, 
                 max_workers: int = 2,
                 enable_turbo_jpeg: bool = True,
                 turbo_jpeg_format: str = "jpeg",
                 turbo_jpeg_quality: int = 95,
                 progress_callback: Optional[Callable] = None):
        """
        初始化并行生成器配置
        
        Args:
            max_workers: 最大工作线程数
            enable_turbo_jpeg: 是否启用TurboJPEG优化
            turbo_jpeg_format: TurboJPEG格式提示
            turbo_jpeg_quality: TurboJPEG质量参数
            progress_callback: 进度回调函数
        """
        self.max_workers = max_workers
        self.enable_turbo_jpeg = enable_turbo_jpeg
        self.turbo_jpeg_format = turbo_jpeg_format
        self.turbo_jpeg_quality = turbo_jpeg_quality
        self.progress_callback = progress_callback


class ParallelGenerator:
    """
    并行生成器
    
    实现样本级多线程并行生成，每个线程独立生成完整的表格样本。
    支持线程安全的浏览器实例管理和TurboJPEG优化。
    """
    
    def __init__(self, base_config_dict: Dict[str, Any], 
                 parallel_config: ParallelGeneratorConfig):
        """
        初始化并行生成器
        
        Args:
            base_config_dict: 基础配置字典
            parallel_config: 并行生成器配置
        """
        self.base_config_dict = base_config_dict
        self.parallel_config = parallel_config
        self.logger = logging.getLogger(__name__)
        
        # 线程安全的计数器
        self._completed_count = 0
        self._failed_count = 0
        self._lock = threading.Lock()
        
        # 统计信息
        self.generation_stats = {
            'total_samples': 0,
            'completed_samples': 0,
            'failed_samples': 0,
            'start_time': None,
            'end_time': None,
            'thread_stats': {}
        }
    
    def generate_samples(self, num_samples: int, 
                        output_dirs: Dict[str, str],
                        start_index: int = 0) -> Dict[str, Any]:
        """
        并行生成多个样本
        
        Args:
            num_samples: 要生成的样本数量
            output_dirs: 输出目录字典
            start_index: 起始样本索引
            
        Returns:
            生成结果统计信息
        """
        self.logger.info(f"开始并行生成 {num_samples} 个样本，使用 {self.parallel_config.max_workers} 个线程")
        
        # 初始化统计信息
        self.generation_stats.update({
            'total_samples': num_samples,
            'completed_samples': 0,
            'failed_samples': 0,
            'start_time': time.time(),
            'end_time': None,
            'thread_stats': {}
        })
        
        # 重置计数器
        with self._lock:
            self._completed_count = 0
            self._failed_count = 0
        
        # 创建任务列表
        tasks = []
        for i in range(num_samples):
            sample_index = start_index + i
            tasks.append({
                'sample_index': sample_index,
                'output_dirs': output_dirs,
                'thread_id': None  # 将在执行时分配
            })
        
        # 执行并行生成
        results = []
        with ThreadPoolExecutor(max_workers=self.parallel_config.max_workers) as executor:
            # 提交所有任务
            future_to_task = {}
            for task in tasks:
                future = executor.submit(self._generate_single_sample, task)
                future_to_task[future] = task
            
            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 更新进度
                    self._update_progress(result)
                    
                except Exception as e:
                    self.logger.error(f"样本 {task['sample_index']} 生成失败: {e}")
                    error_result = {
                        'sample_index': task['sample_index'],
                        'success': False,
                        'error': str(e),
                        'thread_id': task.get('thread_id', 'unknown')
                    }
                    results.append(error_result)
                    self._update_progress(error_result)
        
        # 完成统计
        self.generation_stats['end_time'] = time.time()
        self.generation_stats['completed_samples'] = self._completed_count
        self.generation_stats['failed_samples'] = self._failed_count
        
        total_time = self.generation_stats['end_time'] - self.generation_stats['start_time']
        self.logger.info(f"并行生成完成: {self._completed_count}/{num_samples} 成功, "
                        f"耗时 {total_time:.2f}s")
        
        return {
            'results': results,
            'stats': self.generation_stats,
            'success_rate': self._completed_count / num_samples if num_samples > 0 else 0
        }
    
    def _generate_single_sample(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成单个样本（线程安全）
        
        Args:
            task: 任务信息
            
        Returns:
            生成结果
        """
        sample_index = task['sample_index']
        output_dirs = task['output_dirs']
        thread_id = threading.current_thread().ident
        task['thread_id'] = thread_id
        
        start_time = time.time()
        
        try:
            # 为每个线程创建独立的配置和生成器
            thread_config_dict = self._create_thread_config(sample_index)
            from ..config import RenderConfig
            thread_config = RenderConfig(**thread_config_dict)
            
            # 创建独立的主生成器实例
            generator = MainGenerator(thread_config)
            
            # 生成样本
            result = generator.generate_single_sample(
                sample_index=sample_index,
                output_dirs=output_dirs,
                use_turbo_jpeg=self.parallel_config.enable_turbo_jpeg,
                turbo_format_hint=self.parallel_config.turbo_jpeg_format
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # 记录线程统计信息
            self._record_thread_stats(thread_id, generation_time, True)
            
            return {
                'sample_index': sample_index,
                'success': True,
                'generation_time': generation_time,
                'thread_id': thread_id,
                'result': result
            }
            
        except Exception as e:
            end_time = time.time()
            generation_time = end_time - start_time
            
            # 记录线程统计信息
            self._record_thread_stats(thread_id, generation_time, False)
            
            self.logger.error(f"线程 {thread_id} 生成样本 {sample_index} 失败: {e}")
            
            return {
                'sample_index': sample_index,
                'success': False,
                'error': str(e),
                'generation_time': generation_time,
                'thread_id': thread_id
            }
    
    def _create_thread_config(self, sample_index: int) -> Dict[str, Any]:
        """
        为线程创建独立的配置
        
        Args:
            sample_index: 样本索引
            
        Returns:
            线程配置字典
        """
        # 深拷贝基础配置
        import copy
        thread_config = copy.deepcopy(self.base_config_dict)
        
        # 为每个线程设置独立的随机种子
        base_seed = thread_config.get('seed', 42)
        thread_seed = base_seed + sample_index * 1000 + threading.current_thread().ident % 1000
        thread_config['seed'] = thread_seed
        
        return thread_config
    
    def _update_progress(self, result: Dict[str, Any]) -> None:
        """
        更新进度（线程安全）
        
        Args:
            result: 生成结果
        """
        with self._lock:
            if result['success']:
                self._completed_count += 1
            else:
                self._failed_count += 1
            
            # 调用进度回调
            if self.parallel_config.progress_callback:
                progress_info = {
                    'completed': self._completed_count,
                    'failed': self._failed_count,
                    'total': self.generation_stats['total_samples'],
                    'current_result': result
                }
                try:
                    self.parallel_config.progress_callback(progress_info)
                except Exception as e:
                    self.logger.warning(f"进度回调执行失败: {e}")
    
    def _record_thread_stats(self, thread_id: int, generation_time: float, success: bool) -> None:
        """
        记录线程统计信息（线程安全）
        
        Args:
            thread_id: 线程ID
            generation_time: 生成时间
            success: 是否成功
        """
        with self._lock:
            if thread_id not in self.generation_stats['thread_stats']:
                self.generation_stats['thread_stats'][thread_id] = {
                    'completed_count': 0,
                    'failed_count': 0,
                    'total_time': 0.0,
                    'avg_time': 0.0
                }
            
            thread_stats = self.generation_stats['thread_stats'][thread_id]
            
            if success:
                thread_stats['completed_count'] += 1
            else:
                thread_stats['failed_count'] += 1
            
            thread_stats['total_time'] += generation_time
            total_samples = thread_stats['completed_count'] + thread_stats['failed_count']
            thread_stats['avg_time'] = thread_stats['total_time'] / total_samples if total_samples > 0 else 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            性能摘要信息
        """
        if not self.generation_stats['end_time']:
            return {'error': '生成尚未完成'}
        
        total_time = self.generation_stats['end_time'] - self.generation_stats['start_time']
        total_samples = self.generation_stats['total_samples']
        
        return {
            'total_samples': total_samples,
            'completed_samples': self.generation_stats['completed_samples'],
            'failed_samples': self.generation_stats['failed_samples'],
            'success_rate': self.generation_stats['completed_samples'] / total_samples if total_samples > 0 else 0,
            'total_time': total_time,
            'avg_time_per_sample': total_time / total_samples if total_samples > 0 else 0,
            'samples_per_second': total_samples / total_time if total_time > 0 else 0,
            'thread_count': len(self.generation_stats['thread_stats']),
            'thread_stats': self.generation_stats['thread_stats']
        }
