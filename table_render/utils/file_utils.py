"""
文件工具

提供文件系统操作的辅助函数。
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional
from .turbo_jpeg_saver import TurboJPEGSaver


class FileUtils:
    """
    文件操作工具类

    提供保存图像、标注和元数据的功能。
    """

    # 类级别的TurboJPEG保存器实例（延迟初始化）
    _turbo_saver: Optional[TurboJPEGSaver] = None
    _turbo_saver_quality: int = 95  # 记录当前保存器的质量参数

    @classmethod
    def _get_turbo_saver(cls, quality: int = 95) -> TurboJPEGSaver:
        """
        获取TurboJPEG保存器实例（延迟初始化）

        Args:
            quality: JPEG质量参数

        Returns:
            TurboJPEG保存器实例
        """
        # 如果质量参数变化，重新创建保存器实例
        if cls._turbo_saver is None or cls._turbo_saver_quality != quality:
            cls._turbo_saver = TurboJPEGSaver(quality=quality, enable_turbo=True)
            cls._turbo_saver_quality = quality
        return cls._turbo_saver

    @staticmethod
    def ensure_output_dirs(output_dir: str) -> Dict[str, str]:
        """
        确保输出目录结构存在
        
        Args:
            output_dir: 主输出目录
            
        Returns:
            包含各子目录路径的字典
        """
        logger = logging.getLogger(__name__)
        
        # 创建主输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        subdirs = {
            'images': output_path / 'images',
            'annotations': output_path / 'annotations', 
            'metadata': output_path / 'metadata'
        }
        
        for name, path in subdirs.items():
            path.mkdir(exist_ok=True)
            logger.debug(f"确保目录存在: {path}")
            
        return {name: str(path) for name, path in subdirs.items()}
    
    @staticmethod
    def save_image(image_bytes: bytes, output_path: str) -> None:
        """
        保存图像文件（原始方法，保持向后兼容）

        Args:
            image_bytes: 图像的二进制数据
            output_path: 输出文件路径
        """
        logger = logging.getLogger(__name__)

        try:
            with open(output_path, 'wb') as f:
                f.write(image_bytes)
            logger.debug(f"图像已保存: {output_path}")
        except IOError as e:
            logger.error(f"无法保存图像文件 {output_path}: {e}")
            raise

    @classmethod
    def save_image_optimized(cls, image_bytes: bytes, output_path: str,
                           use_turbo: bool = True, format_hint: Optional[str] = None,
                           quality: int = 95) -> None:
        """
        优化的图像保存方法（使用TurboJPEG加速）

        Args:
            image_bytes: 图像的二进制数据
            output_path: 输出文件路径
            use_turbo: 是否使用TurboJPEG优化
            format_hint: 格式提示 ('jpeg', 'png', 'auto')
            quality: JPEG质量参数 (1-100)
        """
        logger = logging.getLogger(__name__)

        if use_turbo:
            try:
                turbo_saver = cls._get_turbo_saver(quality)
                success = turbo_saver.save_image_bytes(image_bytes, output_path, format_hint)
                if success:
                    logger.debug(f"图像已保存（TurboJPEG优化，质量={quality}）: {output_path}")
                    return
                else:
                    logger.warning("TurboJPEG保存失败，回退到原始方法")
            except Exception as e:
                logger.warning(f"TurboJPEG保存异常，回退到原始方法: {e}")

        # 回退到原始方法
        cls.save_image(image_bytes, output_path)
    
    @staticmethod
    def save_json(data: Dict[str, Any], output_path: str) -> None:
        """
        保存JSON文件
        
        Args:
            data: 要保存的数据
            output_path: 输出文件路径
        """
        logger = logging.getLogger(__name__)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.debug(f"JSON文件已保存: {output_path}")
        except IOError as e:
            logger.error(f"无法保存JSON文件 {output_path}: {e}")
            raise
    
    @classmethod
    def save_sample(
        cls,
        sample_index: int,
        image_bytes: bytes,
        annotations: Dict[str, Any],
        metadata: Dict[str, Any],
        output_dirs: Dict[str, str],
        label_suffix: str = None,
        use_turbo: bool = False,
        format_hint: Optional[str] = None,
        quality: int = 95
    ) -> None:
        """
        保存单个样本的所有文件

        Args:
            sample_index: 样本索引
            image_bytes: 图像数据
            annotations: 标注数据
            metadata: 元数据
            output_dirs: 输出目录字典
            label_suffix: 标注文件后缀，如果提供则添加到标注文件名中
            use_turbo: 是否使用TurboJPEG优化
            format_hint: 格式提示 ('jpeg', 'png', 'auto')
            quality: JPEG质量参数 (1-100)
        """
        logger = logging.getLogger(__name__)
        logger.debug(f"开始保存样本 {sample_index}，标注内容: {annotations}")

        # 生成文件名
        base_filename = f"{sample_index:06d}"

        # 保存图像
        image_path = os.path.join(output_dirs['images'], f"{base_filename}.png")
        if use_turbo:
            cls.save_image_optimized(image_bytes, image_path, use_turbo=True,
                                   format_hint=format_hint, quality=quality)
        else:
            cls.save_image(image_bytes, image_path)
        logger.debug(f"图像已保存: {image_path}")

        # 保存标注 - 支持后缀
        if label_suffix:
            annotation_filename = f"{base_filename}{label_suffix}.json"
        else:
            annotation_filename = f"{base_filename}.json"
        annotation_path = os.path.join(output_dirs['annotations'], annotation_filename)
        cls.save_json(annotations, annotation_path)
        logger.debug(f"标注已保存: {annotation_path}")

        # 保存元数据
        metadata_path = os.path.join(output_dirs['metadata'], f"{base_filename}.json")
        cls.save_json(metadata, metadata_path)
        logger.debug(f"元数据已保存: {metadata_path}")

        logger.debug(f"样本 {sample_index} 所有文件保存完成")
