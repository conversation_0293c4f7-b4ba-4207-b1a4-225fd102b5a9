"""
资源监控工具

监控系统资源使用情况，为并行处理提供资源管理支持。
"""

import logging
import psutil
import threading
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ResourceSnapshot:
    """资源快照"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    active_threads: int


class ResourceMonitor:
    """
    系统资源监控器
    
    监控CPU、内存等系统资源使用情况，为并行处理提供资源管理决策支持。
    """
    
    def __init__(self, monitoring_interval: float = 1.0):
        """
        初始化资源监控器
        
        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.monitoring_interval = monitoring_interval
        self.logger = logging.getLogger(__name__)
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.snapshots = []
        self.max_snapshots = 1000  # 最多保留1000个快照
        self.lock = threading.Lock()
        
        # 获取系统信息
        self.cpu_count = psutil.cpu_count()
        self.total_memory_gb = psutil.virtual_memory().total / (1024**3)
        
        self.logger.info(f"系统资源信息 - CPU核心数: {self.cpu_count}, 总内存: {self.total_memory_gb:.1f}GB")
    
    def start_monitoring(self):
        """开始资源监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("资源监控已启动")
    
    def stop_monitoring(self):
        """停止资源监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("资源监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                snapshot = self._take_snapshot()
                
                with self.lock:
                    self.snapshots.append(snapshot)
                    # 限制快照数量
                    if len(self.snapshots) > self.max_snapshots:
                        self.snapshots.pop(0)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.warning(f"资源监控异常: {e}")
                time.sleep(self.monitoring_interval)
    
    def _take_snapshot(self) -> ResourceSnapshot:
        """获取资源快照"""
        memory_info = psutil.virtual_memory()
        
        return ResourceSnapshot(
            timestamp=time.time(),
            cpu_percent=psutil.cpu_percent(interval=0.1),
            memory_percent=memory_info.percent,
            memory_used_mb=memory_info.used / (1024**2),
            memory_available_mb=memory_info.available / (1024**2),
            active_threads=threading.active_count()
        )
    
    def get_current_resource_usage(self) -> Dict[str, Any]:
        """
        获取当前资源使用情况
        
        Returns:
            当前资源使用情况字典
        """
        snapshot = self._take_snapshot()
        
        return {
            'cpu_percent': snapshot.cpu_percent,
            'memory_percent': snapshot.memory_percent,
            'memory_used_mb': snapshot.memory_used_mb,
            'memory_available_mb': snapshot.memory_available_mb,
            'active_threads': snapshot.active_threads,
            'cpu_count': self.cpu_count,
            'total_memory_gb': self.total_memory_gb
        }
    
    def get_resource_statistics(self, duration_minutes: float = 5.0) -> Dict[str, Any]:
        """
        获取指定时间段内的资源统计信息
        
        Args:
            duration_minutes: 统计时间段（分钟）
            
        Returns:
            资源统计信息
        """
        cutoff_time = time.time() - (duration_minutes * 60)
        
        with self.lock:
            recent_snapshots = [
                s for s in self.snapshots 
                if s.timestamp >= cutoff_time
            ]
        
        if not recent_snapshots:
            return self.get_current_resource_usage()
        
        # 计算统计信息
        cpu_values = [s.cpu_percent for s in recent_snapshots]
        memory_values = [s.memory_percent for s in recent_snapshots]
        thread_values = [s.active_threads for s in recent_snapshots]
        
        return {
            'duration_minutes': duration_minutes,
            'sample_count': len(recent_snapshots),
            'cpu_avg': sum(cpu_values) / len(cpu_values),
            'cpu_max': max(cpu_values),
            'cpu_min': min(cpu_values),
            'memory_avg': sum(memory_values) / len(memory_values),
            'memory_max': max(memory_values),
            'memory_min': min(memory_values),
            'threads_avg': sum(thread_values) / len(thread_values),
            'threads_max': max(thread_values),
            'threads_min': min(thread_values),
            'cpu_count': self.cpu_count,
            'total_memory_gb': self.total_memory_gb
        }
    
    def check_resource_availability(self, required_memory_mb: float = 1000,
                                  max_cpu_percent: float = 90.0) -> Dict[str, Any]:
        """
        检查资源可用性
        
        Args:
            required_memory_mb: 所需内存（MB）
            max_cpu_percent: 最大CPU使用率
            
        Returns:
            资源可用性检查结果
        """
        current = self.get_current_resource_usage()
        
        memory_available = current['memory_available_mb'] >= required_memory_mb
        cpu_available = current['cpu_percent'] <= max_cpu_percent
        
        return {
            'memory_available': memory_available,
            'cpu_available': cpu_available,
            'overall_available': memory_available and cpu_available,
            'current_memory_available_mb': current['memory_available_mb'],
            'required_memory_mb': required_memory_mb,
            'current_cpu_percent': current['cpu_percent'],
            'max_cpu_percent': max_cpu_percent,
            'recommendation': self._get_resource_recommendation(current)
        }
    
    def _get_resource_recommendation(self, current_usage: Dict[str, Any]) -> str:
        """
        根据当前资源使用情况提供建议
        
        Args:
            current_usage: 当前资源使用情况
            
        Returns:
            资源使用建议
        """
        cpu_percent = current_usage['cpu_percent']
        memory_percent = current_usage['memory_percent']
        
        if cpu_percent > 90:
            return "CPU使用率过高，建议减少并行线程数"
        elif memory_percent > 90:
            return "内存使用率过高，建议减少并发任务或增加系统内存"
        elif cpu_percent > 70 and memory_percent > 70:
            return "系统负载较高，建议适度减少并发度"
        elif cpu_percent < 30 and memory_percent < 50:
            return "系统资源充足，可以考虑增加并发度"
        else:
            return "系统资源使用正常"
    
    def suggest_optimal_workers(self, base_workers: int) -> int:
        """
        根据当前资源情况建议最优工作线程数
        
        Args:
            base_workers: 基础工作线程数
            
        Returns:
            建议的工作线程数
        """
        current = self.get_current_resource_usage()
        cpu_percent = current['cpu_percent']
        memory_percent = current['memory_percent']
        
        # 根据资源使用情况调整
        if cpu_percent > 80 or memory_percent > 80:
            # 资源紧张，减少线程数
            return max(1, base_workers // 2)
        elif cpu_percent > 60 or memory_percent > 60:
            # 资源适中，略微减少
            return max(1, int(base_workers * 0.8))
        elif cpu_percent < 30 and memory_percent < 40:
            # 资源充足，可以增加
            return min(self.cpu_count, int(base_workers * 1.5))
        else:
            # 资源正常，保持原有设置
            return base_workers
    
    def __del__(self):
        """析构函数，确保停止监控"""
        self.stop_monitoring()
