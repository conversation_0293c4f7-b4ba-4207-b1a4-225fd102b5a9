# TableRender 优化器模块

## 概述

优化器模块提供各种性能优化工具，专注于提升TableRender的图像生成和保存速度。

## TurboJPEG图像序列化优化器

### 功能特性

- **高速JPEG编码**: 使用PyTurboJPEG库，相比PIL提升3-5倍保存速度
- **优雅降级**: TurboJPEG不可用时自动使用PIL备选方案
- **智能格式选择**: 支持JPEG和PNG格式，可根据文件扩展名或提示自动选择
- **兼容性**: 完全兼容现有的字节数据接口
- **配置灵活**: 支持质量参数和格式提示配置

### 安装依赖

```bash
pip install PyTurboJPEG
```

### 基本使用

```python
from table_render.optimizers import TurboJPEGSaver
from PIL import Image

# 创建保存器
saver = TurboJPEGSaver(quality=95, enable_turbo=True)

# 保存PIL图像
image = Image.open("input.png")
success = saver.save_image(image, "output.jpg", format_hint="jpeg")

# 保存字节数据（兼容现有接口）
with open("input.png", "rb") as f:
    image_bytes = f.read()
success = saver.save_image_bytes(image_bytes, "output.jpg", format_hint="jpeg")
```

### 配置参数

#### TurboJPEGSaver初始化参数

- `quality` (int): JPEG质量参数，范围1-100，默认95
- `enable_turbo` (bool): 是否启用TurboJPEG加速，默认True

#### save_image方法参数

- `image`: PIL图像对象或numpy数组
- `file_path`: 保存路径
- `format_hint`: 格式提示，可选值：
  - `"jpeg"`: 强制使用JPEG格式
  - `"png"`: 强制使用PNG格式
  - `"auto"`: 自动选择（优先JPEG）
  - `None`: 根据文件扩展名判断

### 性能对比

| 保存方式 | 平均耗时 | 相对性能 |
|---------|----------|----------|
| PIL JPEG | 7.8秒 | 1x |
| TurboJPEG | 1.5-2.5秒 | 3-5x |

### 集成示例

在MainGenerator中集成TurboJPEG优化器：

```python
class MainGenerator:
    def __init__(self, config, debug_mode=False):
        # ... 其他初始化代码 ...
        
        # 创建TurboJPEG保存器
        self.turbo_saver = TurboJPEGSaver(quality=95, enable_turbo=True)
    
    def save_sample_optimized(self, sample_index, image_bytes, ...):
        # 使用TurboJPEG优化保存
        image_path = f"output/{sample_index:06d}.jpg"
        success = self.turbo_saver.save_image_bytes(
            image_bytes, image_path, format_hint="jpeg"
        )
        
        if not success:
            # 回退到原始方法
            FileUtils.save_image(image_bytes, image_path)
```

### 错误处理

TurboJPEGSaver具有完善的错误处理机制：

1. **导入失败**: PyTurboJPEG未安装时自动禁用TurboJPEG，使用PIL备选方案
2. **编码失败**: TurboJPEG编码失败时自动回退到PIL
3. **格式转换**: 自动处理图像模式转换（如RGBA→RGB）
4. **目录创建**: 自动创建输出目录

### 调试信息

使用 `get_stats()` 方法获取保存器状态：

```python
saver = TurboJPEGSaver()
stats = saver.get_stats()
print(f"TurboJPEG可用: {stats['turbo_available']}")
print(f"质量设置: {stats['quality']}")
print(f"TurboJPEG启用: {stats['enable_turbo']}")
```

### 测试验证

运行测试脚本验证功能：

```bash
python test_turbo_jpeg.py
```

测试内容包括：
- 功能测试：验证JPEG和PNG保存功能
- 性能测试：对比TurboJPEG和PIL的保存速度
- 格式检测：验证智能格式选择功能

## 未来扩展

优化器模块将在后续版本中添加：
- 异步文件管理器（AsyncFileManager）
- 图像处理优化器
- 内存管理工具

## 注意事项

1. **依赖安装**: 确保安装PyTurboJPEG依赖
2. **格式选择**: JPEG格式速度更快但有损压缩，PNG格式无损但速度较慢
3. **质量设置**: 质量参数影响文件大小和保存速度，建议使用90-95
4. **兼容性**: 完全向后兼容，可以安全替换现有的保存方法
