"""
异步文件管理器

实现异步I/O文件保存功能，减少等待时间，提升整体性能。
支持图像、JSON标注和元数据的异步批量保存。
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Union, Optional
from concurrent.futures import ThreadPoolExecutor
import aiofiles

from .turbo_jpeg_saver import TurboJPEGSaver


class AsyncFileManager:
    """
    异步文件管理器
    
    提供异步I/O文件保存功能，支持图像、JSON和文本文件的高效保存。
    通过异步处理减少I/O等待时间，提升整体性能。
    """
    
    def __init__(self, max_concurrent_writes: int = 10):
        """
        初始化异步文件管理器
        
        Args:
            max_concurrent_writes: 最大并发写入数量
        """
        self.max_concurrent_writes = max_concurrent_writes
        self.logger = logging.getLogger(__name__)
        self.semaphore = asyncio.Semaphore(max_concurrent_writes)
        
        # 初始化TurboJPEG保存器
        self.turbo_saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        
        # 线程池用于CPU密集型操作（图像编码）
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    async def save_image_async(self, image, file_path: Union[str, Path], 
                              format_hint: Optional[str] = None) -> bool:
        """
        异步保存图像文件
        
        Args:
            image: PIL图像对象或numpy数组
            file_path: 保存路径
            format_hint: 格式提示
            
        Returns:
            保存是否成功
        """
        async with self.semaphore:
            try:
                # 在线程池中执行图像保存（CPU密集型操作）
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.thread_pool,
                    self.turbo_saver.save_image,
                    image, file_path, format_hint
                )
                return result
            except Exception as e:
                self.logger.error(f"异步保存图像失败 {file_path}: {e}")
                return False
    
    async def save_json_async(self, data: Dict[str, Any], 
                             file_path: Union[str, Path]) -> bool:
        """
        异步保存JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 保存路径
            
        Returns:
            保存是否成功
        """
        async with self.semaphore:
            try:
                file_path = Path(file_path)
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 序列化JSON数据
                json_str = json.dumps(data, ensure_ascii=False, indent=2)
                
                # 异步写入文件
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    await f.write(json_str)
                
                return True
            except Exception as e:
                self.logger.error(f"异步保存JSON失败 {file_path}: {e}")
                return False
    
    async def save_text_async(self, content: str, 
                             file_path: Union[str, Path]) -> bool:
        """
        异步保存文本文件
        
        Args:
            content: 文本内容
            file_path: 保存路径
            
        Returns:
            保存是否成功
        """
        async with self.semaphore:
            try:
                file_path = Path(file_path)
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    await f.write(content)
                
                return True
            except Exception as e:
                self.logger.error(f"异步保存文本失败 {file_path}: {e}")
                return False
    
    async def save_sample_batch_async(self, sample_data: Dict[str, Any], 
                                     base_path: Union[str, Path],
                                     sample_index: int) -> Dict[str, bool]:
        """
        异步批量保存单个样本的所有文件
        
        Args:
            sample_data: 样本数据，包含image, annotation, metadata等
            base_path: 基础路径
            sample_index: 样本索引
            
        Returns:
            各文件保存结果的字典
        """
        base_path = Path(base_path)
        results = {}
        
        # 准备保存任务
        tasks = []
        
        # 保存图像
        if 'image' in sample_data:
            image_path = base_path / f"sample_{sample_index:06d}.jpg"
            task = self.save_image_async(
                sample_data['image'], 
                image_path, 
                format_hint='jpeg'
            )
            tasks.append(('image', task))
        
        # 保存标注
        if 'annotation' in sample_data:
            annotation_path = base_path / f"sample_{sample_index:06d}.json"
            task = self.save_json_async(sample_data['annotation'], annotation_path)
            tasks.append(('annotation', task))
        
        # 保存元数据
        if 'metadata' in sample_data:
            metadata_path = base_path / f"sample_{sample_index:06d}_metadata.json"
            task = self.save_json_async(sample_data['metadata'], metadata_path)
            tasks.append(('metadata', task))
        
        # 并发执行所有保存任务
        if tasks:
            task_results = await asyncio.gather(
                *[task for _, task in tasks],
                return_exceptions=True
            )
            
            # 收集结果
            for i, (file_type, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    self.logger.error(f"保存{file_type}时发生异常: {result}")
                    results[file_type] = False
                else:
                    results[file_type] = result
        
        return results
    
    async def save_multiple_samples_async(self, samples_data: List[Dict[str, Any]], 
                                         base_path: Union[str, Path]) -> List[Dict[str, bool]]:
        """
        异步批量保存多个样本
        
        Args:
            samples_data: 多个样本的数据列表
            base_path: 基础路径
            
        Returns:
            每个样本的保存结果列表
        """
        tasks = []
        for i, sample_data in enumerate(samples_data):
            task = self.save_sample_batch_async(sample_data, base_path, i)
            tasks.append(task)
        
        # 并发执行所有样本的保存
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"保存样本{i}时发生异常: {result}")
                processed_results.append({'error': True})
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_performance_info(self) -> Dict[str, Any]:
        """
        获取性能信息
        
        Returns:
            性能信息字典
        """
        return {
            'max_concurrent_writes': self.max_concurrent_writes,
            'turbo_jpeg_info': self.turbo_saver.get_performance_info(),
            'thread_pool_workers': self.thread_pool._max_workers
        }
    
    async def close(self):
        """关闭异步文件管理器，清理资源"""
        try:
            self.thread_pool.shutdown(wait=True)
        except Exception as e:
            self.logger.warning(f"关闭线程池时发生错误: {e}")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if hasattr(self, 'thread_pool'):
                self.thread_pool.shutdown(wait=False)
        except:
            pass
