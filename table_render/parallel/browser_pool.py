"""
浏览器实例池管理

实现浏览器实例的复用和管理，减少浏览器启动开销，支持多线程并发访问。
提供异常处理和自动重启机制，确保浏览器实例的稳定性。
"""

import asyncio
import logging
import threading
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
from concurrent.futures import ThreadPoolExecutor
import time

# 导入渲染器
from ..renderers.html_renderer import HtmlRenderer


class BrowserInstance:
    """
    浏览器实例封装
    
    封装单个浏览器实例，提供线程安全的访问和异常恢复机制。
    """
    
    def __init__(self, instance_id: int):
        """
        初始化浏览器实例
        
        Args:
            instance_id: 实例ID
        """
        self.instance_id = instance_id
        self.renderer: Optional[HtmlRenderer] = None
        self.is_healthy = True
        self.last_used = time.time()
        self.use_count = 0
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> bool:
        """
        初始化浏览器实例
        
        Returns:
            初始化是否成功
        """
        try:
            self.renderer = await HtmlRenderer.create_async()
            self.is_healthy = True
            self.logger.debug(f"浏览器实例 {self.instance_id} 初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"浏览器实例 {self.instance_id} 初始化失败: {e}")
            self.is_healthy = False
            return False
    
    async def render_table(self, html_content: str, css_content: str, 
                          debug_mode: bool = False, 
                          debug_output_dir: Optional[str] = None) -> Optional[bytes]:
        """
        渲染表格
        
        Args:
            html_content: HTML内容
            css_content: CSS内容
            debug_mode: 调试模式
            debug_output_dir: 调试输出目录
            
        Returns:
            渲染结果的图像字节数据，失败时返回None
        """
        with self.lock:
            if not self.is_healthy or not self.renderer:
                return None
            
            try:
                # 设置调试模式
                if debug_mode and debug_output_dir:
                    self.renderer.debug_mode = True
                    self.renderer.debug_output_dir = debug_output_dir
                else:
                    self.renderer.debug_mode = False
                
                # 执行渲染
                result = await self.renderer.render_async(html_content, css_content)
                
                # 更新使用统计
                self.last_used = time.time()
                self.use_count += 1
                
                return result
                
            except Exception as e:
                self.logger.error(f"浏览器实例 {self.instance_id} 渲染失败: {e}")
                self.is_healthy = False
                return None
    
    async def close(self):
        """关闭浏览器实例"""
        with self.lock:
            if self.renderer:
                try:
                    await self.renderer.close_async()
                    self.logger.debug(f"浏览器实例 {self.instance_id} 已关闭")
                except Exception as e:
                    self.logger.warning(f"关闭浏览器实例 {self.instance_id} 时发生错误: {e}")
                finally:
                    self.renderer = None
                    self.is_healthy = False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取实例统计信息"""
        return {
            'instance_id': self.instance_id,
            'is_healthy': self.is_healthy,
            'use_count': self.use_count,
            'last_used': self.last_used,
            'idle_time': time.time() - self.last_used
        }


class BrowserPool:
    """
    浏览器实例池
    
    管理多个浏览器实例，提供实例复用、负载均衡和故障恢复功能。
    支持多线程并发访问，确保线程安全。
    """
    
    def __init__(self, max_instances: int = 8):
        """
        初始化浏览器实例池
        
        Args:
            max_instances: 最大实例数量
        """
        self.max_instances = max_instances
        self.instances: Dict[int, BrowserInstance] = {}
        self.available_instances = asyncio.Queue()
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        self.is_initialized = False
        self.stats = {
            'total_renders': 0,
            'failed_renders': 0,
            'instance_restarts': 0
        }
    
    async def initialize(self) -> bool:
        """
        初始化浏览器实例池
        
        Returns:
            初始化是否成功
        """
        if self.is_initialized:
            return True
        
        self.logger.info(f"初始化浏览器实例池，最大实例数: {self.max_instances}")
        
        # 创建浏览器实例
        success_count = 0
        for i in range(self.max_instances):
            instance = BrowserInstance(i)
            if await instance.initialize():
                self.instances[i] = instance
                await self.available_instances.put(instance)
                success_count += 1
            else:
                self.logger.warning(f"浏览器实例 {i} 初始化失败")
        
        if success_count > 0:
            self.is_initialized = True
            self.logger.info(f"浏览器实例池初始化完成，成功创建 {success_count}/{self.max_instances} 个实例")
            return True
        else:
            self.logger.error("浏览器实例池初始化失败，没有可用实例")
            return False
    
    @asynccontextmanager
    async def get_instance(self):
        """
        获取浏览器实例的上下文管理器
        
        使用方式:
            async with browser_pool.get_instance() as instance:
                result = await instance.render_table(html, css)
        """
        if not self.is_initialized:
            raise RuntimeError("浏览器实例池未初始化")
        
        instance = None
        try:
            # 获取可用实例
            instance = await self.available_instances.get()
            
            # 检查实例健康状态
            if not instance.is_healthy:
                # 尝试重启实例
                await self._restart_instance(instance)
            
            yield instance
            
        except Exception as e:
            self.logger.error(f"使用浏览器实例时发生错误: {e}")
            if instance:
                instance.is_healthy = False
            raise
        finally:
            # 归还实例到池中
            if instance:
                await self.available_instances.put(instance)
    
    async def _restart_instance(self, instance: BrowserInstance) -> bool:
        """
        重启浏览器实例
        
        Args:
            instance: 要重启的实例
            
        Returns:
            重启是否成功
        """
        try:
            self.logger.info(f"重启浏览器实例 {instance.instance_id}")
            
            # 关闭旧实例
            await instance.close()
            
            # 重新初始化
            if await instance.initialize():
                self.stats['instance_restarts'] += 1
                self.logger.info(f"浏览器实例 {instance.instance_id} 重启成功")
                return True
            else:
                self.logger.error(f"浏览器实例 {instance.instance_id} 重启失败")
                return False
                
        except Exception as e:
            self.logger.error(f"重启浏览器实例 {instance.instance_id} 时发生错误: {e}")
            return False
    
    async def render_table_with_pool(self, html_content: str, css_content: str,
                                   debug_mode: bool = False,
                                   debug_output_dir: Optional[str] = None) -> Optional[bytes]:
        """
        使用实例池渲染表格
        
        Args:
            html_content: HTML内容
            css_content: CSS内容
            debug_mode: 调试模式
            debug_output_dir: 调试输出目录
            
        Returns:
            渲染结果的图像字节数据
        """
        try:
            async with self.get_instance() as instance:
                result = await instance.render_table(
                    html_content, css_content, debug_mode, debug_output_dir
                )
                
                if result:
                    self.stats['total_renders'] += 1
                else:
                    self.stats['failed_renders'] += 1
                
                return result
                
        except Exception as e:
            self.logger.error(f"使用实例池渲染表格失败: {e}")
            self.stats['failed_renders'] += 1
            return None
    
    async def close(self):
        """关闭浏览器实例池"""
        self.logger.info("关闭浏览器实例池")
        
        # 关闭所有实例
        close_tasks = []
        for instance in self.instances.values():
            close_tasks.append(instance.close())
        
        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
        
        self.instances.clear()
        self.is_initialized = False
        
        self.logger.info("浏览器实例池已关闭")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取实例池统计信息"""
        instance_stats = []
        for instance in self.instances.values():
            instance_stats.append(instance.get_stats())
        
        return {
            'max_instances': self.max_instances,
            'active_instances': len(self.instances),
            'available_instances': self.available_instances.qsize(),
            'total_renders': self.stats['total_renders'],
            'failed_renders': self.stats['failed_renders'],
            'instance_restarts': self.stats['instance_restarts'],
            'success_rate': (
                self.stats['total_renders'] / 
                max(1, self.stats['total_renders'] + self.stats['failed_renders'])
            ),
            'instances': instance_stats
        }
