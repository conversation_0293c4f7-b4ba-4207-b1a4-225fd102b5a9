"""
样本级并行生成器

实现样本级多线程并行生成核心功能，提供3-6倍整体加速。
每个线程独立生成完整样本，确保线程安全和结果一致性。
"""

import asyncio
import logging
import numpy as np
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import threading

# 使用绝对导入避免config目录冲突
import importlib.util
from pathlib import Path as PathLib

# 直接从config.py文件导入
_config_file = PathLib(__file__).parent.parent / "config.py"
_spec = importlib.util.spec_from_file_location("table_render_config", _config_file)
_config_module = importlib.util.module_from_spec(_spec)
_spec.loader.exec_module(_config_module)
RenderConfig = _config_module.RenderConfig
from ..resolver import Resolver
from ..builders.structure_builder import StructureBuilder
from ..builders.content_builder import ContentBuilder
from ..builders.style_builder import StyleBuilder
from ..utils.annotation_converter import AnnotationConverter
from ..utils.file_utils import FileUtils
from ..utils.performance_profiler import profile_stage
from ..utils.resource_monitor import ResourceMonitor
from ..postprocessors.image_augmentor import ImageAugmentor
from ..optimizers import TurboJPEGSaver, AsyncFileManager
from .browser_pool import BrowserPool


class ParallelSampleGenerator:
    """
    样本级并行生成器
    
    实现样本级多线程并行生成，每个线程独立生成完整样本。
    提供线程安全的随机种子分配、独立浏览器实例管理和高效文件保存。
    """
    
    def __init__(self, config: RenderConfig, debug_mode: bool = False):
        """
        初始化并行样本生成器
        
        Args:
            config: 渲染配置
            debug_mode: 调试模式
        """
        self.config = config
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        
        # 获取性能配置
        self.perf_config = config.performance
        if not self.perf_config:
            # 如果没有性能配置，创建默认配置
            PerformanceConfig = _config_module.PerformanceConfig
            self.perf_config = PerformanceConfig()
        
        # 初始化组件
        self.resolver = Resolver()
        self.annotation_converter = AnnotationConverter()
        self.resource_monitor = ResourceMonitor()
        
        # 线程安全的随机状态管理
        self.main_random_state = np.random.RandomState(config.seed)
        self.seed_lock = threading.Lock()
        
        # 性能统计
        self.stats = {
            'total_samples': 0,
            'successful_samples': 0,
            'failed_samples': 0,
            'total_time': 0.0,
            'average_time_per_sample': 0.0
        }
        
        self.logger.info(f"并行生成器初始化完成 - 并行模式: {self.perf_config.enable_parallel}")
    
    async def generate_samples_parallel(self, num_samples: int) -> Dict[str, Any]:
        """
        并行生成样本
        
        Args:
            num_samples: 要生成的样本数量
            
        Returns:
            生成结果统计
        """
        start_time = time.time()
        self.logger.info(f"开始并行生成 {num_samples} 个样本")
        
        # 启动资源监控
        self.resource_monitor.start_monitoring()
        
        try:
            # 确保输出目录存在
            output_dirs = FileUtils.ensure_output_dirs(self.config.output.output_dir)
            
            # 根据配置选择生成模式
            if self.perf_config.enable_parallel and num_samples > 1:
                results = await self._generate_parallel_mode(num_samples, output_dirs)
            else:
                results = await self._generate_sequential_mode(num_samples, output_dirs)
            
            # 统计结果
            total_time = time.time() - start_time
            successful = sum(1 for r in results if r.get('success', False))
            failed = num_samples - successful
            
            self.stats.update({
                'total_samples': num_samples,
                'successful_samples': successful,
                'failed_samples': failed,
                'total_time': total_time,
                'average_time_per_sample': total_time / max(1, successful)
            })
            
            self.logger.info(f"并行生成完成 - 成功: {successful}, 失败: {failed}, 总耗时: {total_time:.2f}秒")
            
            return {
                'success': True,
                'stats': self.stats,
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"并行生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'stats': self.stats
            }
        finally:
            # 停止资源监控
            self.resource_monitor.stop_monitoring()
            
            # 清理临时文件
            try:
                self.resolver.cleanup_temp_files()
            except Exception as e:
                self.logger.warning(f"清理临时文件失败: {e}")
    
    async def _generate_parallel_mode(self, num_samples: int, output_dirs: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        并行模式生成
        
        Args:
            num_samples: 样本数量
            output_dirs: 输出目录
            
        Returns:
            生成结果列表
        """
        # 获取实际工作线程数
        actual_workers = self.perf_config.get_actual_workers(num_samples)
        actual_browsers = self.perf_config.get_actual_browser_instances(actual_workers)
        
        self.logger.info(f"并行模式 - 工作线程: {actual_workers}, 浏览器实例: {actual_browsers}")
        
        # 初始化浏览器实例池
        browser_pool = BrowserPool(max_instances=actual_browsers)
        if not await browser_pool.initialize():
            raise RuntimeError("浏览器实例池初始化失败")
        
        # 初始化异步文件管理器
        async_file_manager = AsyncFileManager(max_concurrent_writes=actual_workers)
        
        try:
            # 创建线程池
            with ThreadPoolExecutor(max_workers=actual_workers) as executor:
                # 提交所有任务
                futures = []
                for i in range(num_samples):
                    future = executor.submit(
                        self._generate_single_sample_sync,
                        i, browser_pool, async_file_manager, output_dirs
                    )
                    futures.append((i, future))
                
                # 收集结果
                results = [None] * num_samples
                for sample_index, future in futures:
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        results[sample_index] = result
                    except Exception as e:
                        self.logger.error(f"样本 {sample_index} 生成失败: {e}")
                        results[sample_index] = {'success': False, 'error': str(e)}
                
                return results
                
        finally:
            # 清理资源
            await browser_pool.close()
            await async_file_manager.close()
    
    async def _generate_sequential_mode(self, num_samples: int, output_dirs: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        串行模式生成（向后兼容）
        
        Args:
            num_samples: 样本数量
            output_dirs: 输出目录
            
        Returns:
            生成结果列表
        """
        self.logger.info("串行模式生成")
        
        # 初始化单个浏览器实例
        browser_pool = BrowserPool(max_instances=1)
        if not await browser_pool.initialize():
            raise RuntimeError("浏览器实例初始化失败")
        
        # 初始化异步文件管理器
        async_file_manager = AsyncFileManager(max_concurrent_writes=1)
        
        try:
            results = []
            for i in range(num_samples):
                result = self._generate_single_sample_sync(
                    i, browser_pool, async_file_manager, output_dirs
                )
                results.append(result)
            
            return results
            
        finally:
            await browser_pool.close()
            await async_file_manager.close()
    
    def _generate_single_sample_sync(self, sample_index: int, 
                                   browser_pool: BrowserPool,
                                   async_file_manager: AsyncFileManager,
                                   output_dirs: Dict[str, str]) -> Dict[str, Any]:
        """
        生成单个样本（同步版本，在线程池中执行）
        
        Args:
            sample_index: 样本索引
            browser_pool: 浏览器实例池
            async_file_manager: 异步文件管理器
            output_dirs: 输出目录
            
        Returns:
            生成结果
        """
        try:
            # 在新的事件循环中运行异步生成
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self._generate_single_sample_async(
                        sample_index, browser_pool, async_file_manager, output_dirs
                    )
                )
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"样本 {sample_index} 生成异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_thread_safe_seed(self) -> int:
        """
        生成线程安全的随机种子

        Returns:
            随机种子
        """
        with self.seed_lock:
            return self.main_random_state.randint(0, 2**32 - 1)

    async def _generate_single_sample_async(self, sample_index: int,
                                          browser_pool: BrowserPool,
                                          async_file_manager: AsyncFileManager,
                                          output_dirs: Dict[str, str]) -> Dict[str, Any]:
        """
        异步生成单个样本

        Args:
            sample_index: 样本索引
            browser_pool: 浏览器实例池
            async_file_manager: 异步文件管理器
            output_dirs: 输出目录

        Returns:
            生成结果
        """
        sample_start_time = time.time()

        try:
            with profile_stage("sample_total", {"sample_index": sample_index}):
                self.logger.info(f"开始生成样本 {sample_index}")

                # 生成独立的随机种子
                sample_seed = self._generate_thread_safe_seed()

                # 解析配置参数
                with profile_stage("config_resolution", {"sample_seed": sample_seed}):
                    resolved_params = self.resolver.resolve(self.config, sample_seed)

                # 构建表格结构
                with profile_stage("structure_building"):
                    structure_builder = StructureBuilder(sample_seed)
                    table_model = structure_builder.build(resolved_params.structure)

                # 填充表格内容
                with profile_stage("content_building"):
                    content_builder = ContentBuilder(sample_seed)
                    content_builder.fill_content(table_model, resolved_params.content)

                # 生成样式
                with profile_stage("style_building"):
                    style_builder = StyleBuilder(sample_seed)
                    transparency_config = resolved_params.postprocessing if resolved_params.postprocessing else None
                    css_string = style_builder.build(resolved_params.style, table_model, transparency_config)

                # 设置调试目录
                debug_output_dir = None
                if self.debug_mode:
                    debug_output_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{sample_index:06d}")
                    Path(debug_output_dir).mkdir(parents=True, exist_ok=True)

                # HTML渲染
                with profile_stage("html_rendering"):
                    html_content = table_model.to_html()
                    image_bytes = await browser_pool.render_table_with_pool(
                        html_content, css_string, self.debug_mode, debug_output_dir
                    )

                    if not image_bytes:
                        raise RuntimeError("HTML渲染失败")

                # 图像后处理
                final_image = None
                final_annotations = None

                if resolved_params.postprocessing and (
                    resolved_params.postprocessing.apply_perspective or
                    resolved_params.postprocessing.apply_background
                ):
                    with profile_stage("image_augmentation"):
                        augmentor = ImageAugmentor(sample_seed, self.debug_mode, debug_output_dir)
                        final_image, final_annotations = await augmentor.process_image_async(
                            image_bytes, resolved_params.postprocessing
                        )
                else:
                    # 无后处理，直接使用渲染结果
                    from PIL import Image
                    import io
                    final_image = Image.open(io.BytesIO(image_bytes))
                    final_annotations = self.annotation_converter.convert_table_to_annotation(
                        table_model, final_image.size
                    )

                # 异步保存文件
                with profile_stage("file_saving"):
                    sample_data = {
                        'image': final_image,
                        'annotation': final_annotations,
                        'metadata': {
                            'sample_index': sample_index,
                            'seed': sample_seed,
                            'resolved_params': resolved_params.dict(),
                            'generation_time': time.time() - sample_start_time
                        }
                    }

                    save_results = await async_file_manager.save_sample_batch_async(
                        sample_data, self.config.output.output_dir, sample_index
                    )

                # 检查保存结果
                if not all(save_results.values()):
                    self.logger.warning(f"样本 {sample_index} 部分文件保存失败: {save_results}")

                sample_duration = time.time() - sample_start_time
                self.logger.info(f"样本 {sample_index} 生成完成，耗时: {sample_duration:.2f}秒")

                return {
                    'success': True,
                    'sample_index': sample_index,
                    'duration': sample_duration,
                    'save_results': save_results
                }

        except Exception as e:
            sample_duration = time.time() - sample_start_time
            self.logger.error(f"样本 {sample_index} 生成失败: {e}，耗时: {sample_duration:.2f}秒")
            return {
                'success': False,
                'sample_index': sample_index,
                'duration': sample_duration,
                'error': str(e)
            }

    def get_generation_stats(self) -> Dict[str, Any]:
        """
        获取生成统计信息

        Returns:
            统计信息字典
        """
        resource_stats = self.resource_monitor.get_resource_statistics()

        return {
            'generation_stats': self.stats,
            'resource_stats': resource_stats,
            'performance_config': {
                'enable_parallel': self.perf_config.enable_parallel,
                'max_workers': self.perf_config.max_workers,
                'resolved_max_workers': self.perf_config.resolve_max_workers(),
                'max_browser_instances': self.perf_config.max_browser_instances
            }
        }
