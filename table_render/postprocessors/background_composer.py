"""
背景图合成器

负责将表格图像合成到背景图上，并处理相应的坐标变换。
"""

import logging
import numpy as np
import cv2
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, Any, Tuple, Optional
import copy
import os
import json
from pathlib import Path


class BackgroundComposer:
    """
    背景图合成器
    
    将表格图像贴到背景图上，并进行随机裁剪，同时更新标注坐标。
    """
    
    def __init__(self, seed: int, debug_mode: bool = False, debug_output_dir: Optional[str] = None):
        """
        初始化背景图合成器

        Args:
            seed: 随机种子
            debug_mode: 是否启用调试模式
            debug_output_dir: 调试输出目录，仅在debug_mode=True时有效
        """
        self.random_state = np.random.RandomState(seed)
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.debug_stage_counter = 0

        if self.debug_mode and self.debug_output_dir:
            # 确保调试输出目录存在
            Path(self.debug_output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"调试模式已启用，输出目录: {self.debug_output_dir}")

    def _save_debug_stage(self, stage_name: str, image: Image.Image, annotations: Optional[Dict[str, Any]],
                         additional_info: Optional[Dict[str, Any]] = None):
        """
        保存调试阶段的图像和标注

        Args:
            stage_name: 阶段名称
            image: 图像
            annotations: 标注数据
            additional_info: 额外信息
        """
        if not self.debug_mode or not self.debug_output_dir:
            return

        self.debug_stage_counter += 1
        stage_prefix = f"stage{self.debug_stage_counter:02d}_{stage_name}"

        # 保存图像
        image_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.png")
        image.save(image_path)
        self.logger.info(f"调试: 保存图像 {image_path}")

        # 保存标注
        if annotations is not None:
            annotation_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.json")
            debug_data = {
                "stage": stage_name,
                "image_size": image.size,
                "annotations": annotations
            }
            if additional_info:
                debug_data.update(additional_info)

            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"调试: 保存标注 {annotation_path}")

        # 生成可视化图像（带标注框）
        if annotations and annotations.get('cells'):
            vis_image = self._create_visualization_image(image, annotations)
            vis_path = os.path.join(self.debug_output_dir, f"{stage_prefix}_with_boxes.png")
            vis_image.save(vis_path)
            self.logger.info(f"调试: 保存可视化图像 {vis_path}")

    def _create_visualization_image(self, image: Image.Image, annotations: Dict[str, Any]) -> Image.Image:
        """
        创建带标注框的可视化图像

        Args:
            image: 原始图像
            annotations: 标注数据

        Returns:
            带标注框的图像
        """
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)

        # 定义颜色
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, cell in enumerate(annotations.get('cells', [])):
            if 'bbox' not in cell:
                continue

            bbox = cell['bbox']
            color = colors[i % len(colors)]

            if isinstance(bbox, dict) and all(key in bbox for key in ['p1', 'p2', 'p3', 'p4']):
                # 四角点格式
                points = [tuple(bbox['p1']), tuple(bbox['p2']), tuple(bbox['p3']), tuple(bbox['p4'])]
                # 绘制多边形
                draw.polygon(points, outline=color, width=2)
                # 标记单元格编号
                draw.text(bbox['p1'], str(i), fill=color)
            elif isinstance(bbox, list) and len(bbox) >= 4:
                # 矩形格式 [x_min, y_min, x_max, y_max]
                x_min, y_min, x_max, y_max = bbox[:4]
                draw.rectangle([x_min, y_min, x_max, y_max], outline=color, width=2)
                # 标记单元格编号
                draw.text((x_min, y_min), str(i), fill=color)

        return vis_image
    
    def compose(self,
               table_image: Image.Image,
               annotations: Optional[Dict[str, Any]],
               background_path: str,
               max_scale_factor: float = None,  # V5.1改进：设为可选参数
               transparency_config=None,
               background_params=None,
               sample_seed: int = None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        将表格图像合成到背景图上

        Args:
            table_image: 表格图像（可能包含透明区域）
            annotations: 标注数据
            background_path: 背景图路径
            max_scale_factor: 最大缩放倍数（V5.1已弃用，保留仅为兼容性）
            transparency_config: V4.3新增：透明度配置参数
            background_params: V5.2修复：背景相关参数，包含margin_control等配置
            sample_seed: V5.2修复：样本种子，用于智能裁剪的随机性控制

        Returns:
            (合成后的图像, 更新后的标注)
        """
        self.logger.debug(f"开始背景图合成，表格尺寸: {table_image.size}")
        self.logger.debug(f"输入标注: {annotations}")

        # V5.1改进：处理max_scale_factor弃用
        if max_scale_factor is not None:
            self.logger.warning("[DEPRECATED] max_scale_factor参数已弃用，现在使用智能动态适应模式")

        try:
            # 调试: 保存原始表格和标注
            self._save_debug_stage("original_table", table_image, annotations, {
                "background_path": background_path,
                "adaptive_scaling": "enabled"  # 更新调试信息
            })

            # 加载背景图
            background_image = self._load_background_image(background_path)

            # 处理表格图像的透明区域
            table_image_processed = self._process_table_transparency(table_image, transparency_config)

            # 调试: 保存处理透明区域后的表格
            if table_image_processed != table_image:
                self._save_debug_stage("processed_transparency", table_image_processed, annotations)

            # V5.1改进：使用新的智能缩放策略
            scale_factor, scaled_table, scaled_annotations = self._calculate_adaptive_scaling(
                table_image_processed, annotations, background_image.size
            )

            # 调试: 保存缩放后的结果
            self._save_debug_stage("scaled_table", scaled_table, scaled_annotations, {
                "scale_factor": scale_factor,
                "background_size": background_image.size
            })

            # V5.1改进：智能背景图适应
            final_background = self._adapt_background_for_table(
                background_image, scaled_table.size
            )

            # 调试: 保存适应后的背景图（如果有变化）
            if final_background != background_image:
                self._save_debug_stage("adapted_background", final_background, None, {
                    "original_background_size": background_image.size,
                    "adapted_background_size": final_background.size,
                    "table_size": scaled_table.size
                })

            # 计算贴图位置
            paste_position = self._calculate_paste_position(
                scaled_table.size, final_background.size
            )

            # 贴图到背景
            composed_image = self._paste_table_to_background(
                final_background, scaled_table, paste_position
            )

            # 更新标注坐标（加上贴图偏移）
            updated_annotations = self._update_annotations_for_paste(
                scaled_annotations, paste_position
            )

            # 调试: 保存贴图后的结果
            self._save_debug_stage("composed_image", composed_image, updated_annotations, {
                "paste_position": paste_position,
                "background_size": final_background.size
            })

            # V4.2新逻辑：智能裁剪或随机裁剪
            if background_params and hasattr(background_params, 'margin_control') and background_params.margin_control:
                # V5.2修复：处理sample_seed为None的情况
                if sample_seed is None:
                    sample_seed = self.random_state.randint(0, 2**31 - 1)

                # 使用智能裁剪（基于真实表格边界和边距控制）
                final_image, final_annotations = self._smart_crop_with_margin(
                    composed_image, updated_annotations, background_params.margin_control, sample_seed
                )
                self.logger.info("使用智能边距裁剪")
            else:
                # 使用原有的随机裁剪
                final_image, final_annotations = self._random_crop_with_table(
                    composed_image, updated_annotations
                )
                self.logger.debug("使用随机裁剪")

            # 调试: 保存最终结果
            self._save_debug_stage("final_result", final_image, final_annotations, {
                "original_composed_size": composed_image.size,
                "final_size": final_image.size,
                "crop_method": "smart_margin" if (background_params and hasattr(background_params, 'margin_control') and background_params.margin_control) else "random"
            })

            self.logger.debug(f"背景图合成完成: {background_path}")
            self.logger.debug(f"最终图像尺寸: {final_image.size}")
            self.logger.debug(f"最终标注: {final_annotations}")
            return final_image, final_annotations
            
        except Exception as e:
            self.logger.error(f"背景图合成失败: {e}")
            # 返回原始图像和标注
            return table_image, annotations
    
    def _load_background_image(self, background_path: str) -> Image.Image:
        """加载背景图"""
        try:
            background = Image.open(background_path)
            # 确保是RGB模式
            if background.mode != 'RGB':
                background = background.convert('RGB')
            return background
        except Exception as e:
            raise ValueError(f"无法加载背景图 {background_path}: {e}")
    
    def _process_table_transparency(self, table_image: Image.Image, transparency_config=None) -> Image.Image:
        """
        处理表格图像的透明区域

        V4.3增强：支持基于CSS生成的半透明表格处理

        Args:
            table_image: 表格图像
            transparency_config: 透明度配置参数

        Returns:
            处理后的表格图像
        """
        # 转换为RGBA模式
        if table_image.mode != 'RGBA':
            table_image = table_image.convert('RGBA')

        # 将图像转换为numpy数组
        img_array = np.array(table_image)

        # V4.3新增：如果启用了透明度，表格已经在CSS阶段处理了透明度
        # 这里主要处理边缘的黑色区域
        if transparency_config and transparency_config.enable_transparency:
            self.logger.debug("处理CSS生成的半透明表格")
            # 对于半透明表格，只处理完全黑色的边缘区域
            black_mask = (
                (img_array[:, :, 0] < 5) &
                (img_array[:, :, 1] < 5) &
                (img_array[:, :, 2] < 5)
            )
        else:
            # 传统模式：处理黑色区域
            black_mask = (
                (img_array[:, :, 0] < 10) &
                (img_array[:, :, 1] < 10) &
                (img_array[:, :, 2] < 10)
            )

        # 将黑色区域设置为透明
        img_array[black_mask, 3] = 0

        return Image.fromarray(img_array, 'RGBA')
    
    def _calculate_adaptive_scaling(self,
                                  table_image: Image.Image,
                                  annotations: Optional[Dict[str, Any]],
                                  background_size: Tuple[int, int]) -> Tuple[float, Image.Image, Optional[Dict[str, Any]]]:
        """
        V5.1新增：智能自适应缩放策略，替代max_scale_factor限制

        Returns:
            (缩放因子, 缩放后的表格图像, 缩放后的标注)
        """
        table_width, table_height = table_image.size
        bg_width, bg_height = background_size

        # 系统安全限制
        MAX_BACKGROUND_SCALE = 20.0  # 最大背景缩放倍数
        MIN_TABLE_SCALE = 0.1        # 最小表格缩放倍数（防止表格过小）

        # 判断表格大小类别
        is_large_table = table_width > 2000 or table_height > 1500

        # 计算是否需要缩放
        if table_width <= bg_width and table_height <= bg_height:
            # 表格能放入背景，不需要缩放
            self.logger.debug(f"[ADAPTIVE_SCALING] 表格适合背景，无需缩放")
            return 1.0, table_image, annotations

        # 计算所需的缩放倍数
        scale_x = table_width / bg_width
        scale_y = table_height / bg_height
        required_scale = max(scale_x, scale_y)

        if is_large_table:
            # 大表格：优先保持表格清晰度，允许更大的背景缩放
            if required_scale <= MAX_BACKGROUND_SCALE:
                self.logger.info(f"[ADAPTIVE_SCALING] 大表格模式：缩放背景图 {required_scale:.2f}倍")
                return self._scale_background_to_fit_table(table_image, annotations, background_size, required_scale)
            else:
                # 即使是大表格，也不能无限缩放背景
                self.logger.warning(f"[ADAPTIVE_SCALING] 大表格超出背景缩放限制，缩放表格")
                table_scale = MAX_BACKGROUND_SCALE / required_scale
                return self._scale_table_to_fit_background(table_image, annotations, table_scale)
        else:
            # 中小表格：平衡背景和表格缩放
            if required_scale <= 5.0:  # 中小表格的背景缩放限制更保守
                self.logger.debug(f"[ADAPTIVE_SCALING] 中小表格：缩放背景图 {required_scale:.2f}倍")
                return self._scale_background_to_fit_table(table_image, annotations, background_size, required_scale)
            else:
                # 缩放表格
                table_scale = max(1.0 / required_scale, MIN_TABLE_SCALE)
                self.logger.debug(f"[ADAPTIVE_SCALING] 中小表格：缩放表格 {table_scale:.2f}倍")
                return self._scale_table_to_fit_background(table_image, annotations, table_scale)

    def _scale_annotations(self, annotations: Optional[Dict[str, Any]], scale_factor: float) -> Optional[Dict[str, Any]]:
        """缩放标注坐标"""
        if annotations is None:
            self.logger.debug("缩放标注: 输入标注为None")
            return None

        self.logger.debug(f"开始缩放标注，缩放因子: {scale_factor}")
        scaled_annotations = copy.deepcopy(annotations)

        for i, cell in enumerate(scaled_annotations.get('cells', [])):
            bbox = cell.get('bbox', {})
            self.logger.debug(f"缩放前单元格{i}: {bbox}")
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    new_x, new_y = x * scale_factor, y * scale_factor
                    bbox[point_key] = [new_x, new_y]
                    self.logger.debug(f"  {point_key}: ({x}, {y}) -> ({new_x}, {new_y})")
            self.logger.debug(f"缩放后单元格{i}: {bbox}")

        self.logger.debug("标注缩放完成")
        return scaled_annotations

    def _scale_background_to_fit_table(self,
                                     table_image: Image.Image,
                                     annotations: Optional[Dict[str, Any]],
                                     background_size: Tuple[int, int],
                                     required_scale: float) -> Tuple[float, Image.Image, Optional[Dict[str, Any]]]:
        """
        缩放背景图以适应表格尺寸，保持表格原始清晰度

        Returns:
            (缩放因子=1.0, 原始表格图像, 原始标注)
        """
        # 背景图需要放大，但我们实际上不在这里处理背景图缩放
        # 而是在compose方法中通过调整背景图尺寸来实现
        # 这里返回原始表格和标注，缩放因子为1.0表示表格未缩放
        self.logger.debug(f"背景图需要放大 {required_scale} 倍以适应表格")
        return 1.0, table_image, annotations

    def _scale_table_to_fit_background(self,
                                     table_image: Image.Image,
                                     annotations: Optional[Dict[str, Any]],
                                     scale_factor: float) -> Tuple[float, Image.Image, Optional[Dict[str, Any]]]:
        """
        缩放表格以适应背景图尺寸

        Returns:
            (缩放因子, 缩放后的表格图像, 缩放后的标注)
        """
        table_width, table_height = table_image.size
        new_width = int(table_width * scale_factor)
        new_height = int(table_height * scale_factor)

        # 对于包含表格线条的图像，使用边缘保持算法
        if scale_factor < 1.0:
            # 缩小时使用NEAREST避免线条模糊
            scaled_table = table_image.resize((new_width, new_height), Image.Resampling.NEAREST)
            self.logger.debug(f"使用NEAREST算法缩小表格，缩放因子: {scale_factor}")
        else:
            # 放大时使用LANCZOS获得更好的质量
            scaled_table = table_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            self.logger.debug(f"使用LANCZOS算法放大表格，缩放因子: {scale_factor}")

        # 缩放标注坐标
        scaled_annotations = self._scale_annotations(annotations, scale_factor)

        return scale_factor, scaled_table, scaled_annotations

    def _scale_background_if_needed(self,
                                   background_image: Image.Image,
                                   table_size: Tuple[int, int],
                                   max_scale_factor: float = None) -> Image.Image:
        """
        V5.1改进：如果需要，缩放背景图以适应表格（保留兼容性）

        Args:
            background_image: 背景图像
            table_size: 表格尺寸
            max_scale_factor: 最大缩放倍数（已弃用，保留仅为兼容性）

        Returns:
            缩放后的背景图像
        """
        if max_scale_factor is not None:
            self.logger.warning("[DEPRECATED] _scale_background_if_needed中的max_scale_factor参数已弃用")

        # 使用新的智能适应方法
        return self._adapt_background_for_table(background_image, table_size)

    def _adapt_background_for_table(self, background_image: Image.Image, table_size: Tuple[int, int]) -> Image.Image:
        """
        V5.1新增：智能背景图适应，确保能容纳表格

        Args:
            background_image: 原始背景图
            table_size: 表格尺寸

        Returns:
            适应后的背景图
        """
        bg_width, bg_height = background_image.size
        table_width, table_height = table_size

        # 计算所需的最小背景尺寸（包含边距）
        required_width = table_width + 200   # 左右各100px边距
        required_height = table_height + 200 # 上下各100px边距

        # 检查是否需要扩展背景
        if bg_width >= required_width and bg_height >= required_height:
            # 背景已经足够大
            self.logger.debug(f"[BACKGROUND_ADAPT] 背景图已足够大，无需调整")
            return background_image

        # 计算需要的缩放倍数
        scale_x = required_width / bg_width if bg_width > 0 else 1.0
        scale_y = required_height / bg_height if bg_height > 0 else 1.0
        required_scale = max(scale_x, scale_y, 1.0)  # 至少不缩小

        # 应用系统安全限制
        MAX_BACKGROUND_SCALE = 20.0
        final_scale = min(required_scale, MAX_BACKGROUND_SCALE)

        # 缩放背景图
        new_width = int(bg_width * final_scale)
        new_height = int(bg_height * final_scale)

        adapted_background = background_image.resize(
            (new_width, new_height), Image.Resampling.LANCZOS
        )

        self.logger.info(f"[BACKGROUND_ADAPT] 背景图适应: {bg_width}x{bg_height} → {new_width}x{new_height}")
        self.logger.info(f"[BACKGROUND_ADAPT] 缩放倍数: {final_scale:.2f} (需求: {required_scale:.2f})")

        if final_scale >= MAX_BACKGROUND_SCALE:
            self.logger.warning(f"[BACKGROUND_ADAPT] 达到最大缩放限制 {MAX_BACKGROUND_SCALE}倍")

        return adapted_background

    def _calculate_paste_position(self, table_size: Tuple[int, int], background_size: Tuple[int, int]) -> Tuple[int, int]:
        """计算表格在背景中的贴图位置（智能位置分布）"""
        table_width, table_height = table_size
        bg_width, bg_height = background_size

        # 使用智能位置分布
        paste_x, paste_y = self._calculate_smart_paste_position(
            table_width, table_height, bg_width, bg_height
        )

        return (paste_x, paste_y)

    def _calculate_smart_paste_position(self, table_width: int, table_height: int,
                                      bg_width: int, bg_height: int) -> tuple:
        """
        智能贴图位置计算：让表格尽可能在背景图的中间位置

        Args:
            table_width: 表格宽度
            table_height: 表格高度
            bg_width: 背景图宽度
            bg_height: 背景图高度

        Returns:
            (paste_x, paste_y): 贴图位置
        """
        # 默认配置：偏向中心
        prefer_center_prob = 0.8

        # 使用简单的边距逻辑
        max_x = max(0, bg_width - table_width)
        max_y = max(0, bg_height - table_height)

        safe_left = 0
        safe_top = 0
        safe_right = max_x
        safe_bottom = max_y

        # 检查是否有足够空间
        if safe_left >= safe_right or safe_top >= safe_bottom:
            self.logger.warning(f"[SMART_PASTE] 背景图空间不足")
            return (0, 0)

        self.logger.info(f"[SMART_PASTE] 安全区域: left={safe_left}, top={safe_top}, right={safe_right}, bottom={safe_bottom}")

        # 决定使用中心偏向还是均匀分布
        if self.random_state.random() < prefer_center_prob:
            # 使用中心偏向分布
            pos_x, pos_y = self._calculate_center_biased_paste_position(
                safe_left, safe_top, safe_right, safe_bottom
            )
            self.logger.info(f"[SMART_PASTE] 使用中心偏向分布: ({pos_x}, {pos_y})")
        else:
            # 使用均匀分布
            pos_x = self.random_state.randint(safe_left, safe_right + 1)
            pos_y = self.random_state.randint(safe_top, safe_bottom + 1)
            self.logger.info(f"[SMART_PASTE] 使用均匀分布: ({pos_x}, {pos_y})")

        return (int(pos_x), int(pos_y))

    def _calculate_center_biased_paste_position(self, safe_left: int, safe_top: int,
                                              safe_right: int, safe_bottom: int) -> tuple:
        """
        计算中心偏向的贴图位置
        """
        import math

        # 计算中心点
        center_x = (safe_left + safe_right) / 2
        center_y = (safe_top + safe_bottom) / 2

        # 计算标准差（固定偏向强度）
        range_x = safe_right - safe_left
        range_y = safe_bottom - safe_top

        std_x = range_x / 4  # 约68%的值在中心1/2区域内
        std_y = range_y / 4

        # 生成正态分布的位置
        u1 = self.random_state.random()
        u2 = self.random_state.random()

        # Box-Muller变换
        z0 = math.sqrt(-2 * math.log(u1)) * math.cos(2 * math.pi * u2)
        z1 = math.sqrt(-2 * math.log(u1)) * math.sin(2 * math.pi * u2)

        # 应用到位置计算
        pos_x = center_x + z0 * std_x
        pos_y = center_y + z1 * std_y

        # 确保位置在安全区域内
        pos_x = max(safe_left, min(safe_right, pos_x))
        pos_y = max(safe_top, min(safe_bottom, pos_y))

        self.logger.debug(f"[CENTER_BIAS_PASTE] 中心点: ({center_x}, {center_y}), 标准差: ({std_x:.1f}, {std_y:.1f})")
        self.logger.debug(f"[CENTER_BIAS_PASTE] 生成位置: ({pos_x:.1f}, {pos_y:.1f})")

        return (pos_x, pos_y)

    def _paste_table_to_background(self,
                                 background: Image.Image,
                                 table: Image.Image,
                                 position: Tuple[int, int]) -> Image.Image:
        """将表格贴到背景上"""
        composed = background.copy()

        # 使用alpha通道进行合成
        if table.mode == 'RGBA':
            composed.paste(table, position, table)
        else:
            composed.paste(table, position)

        return composed

    def _update_annotations_for_paste(self,
                                    annotations: Optional[Dict[str, Any]],
                                    paste_position: Tuple[int, int]) -> Optional[Dict[str, Any]]:
        """更新标注坐标以反映贴图偏移"""
        if annotations is None:
            self.logger.debug("贴图偏移: 输入标注为None")
            return None

        self.logger.debug(f"开始应用贴图偏移，偏移量: {paste_position}")
        updated_annotations = copy.deepcopy(annotations)
        paste_x, paste_y = paste_position

        for i, cell in enumerate(updated_annotations.get('cells', [])):
            bbox = cell.get('bbox', {})
            self.logger.debug(f"贴图偏移前单元格{i}: {bbox}")
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    new_x, new_y = x + paste_x, y + paste_y
                    bbox[point_key] = [new_x, new_y]
                    self.logger.debug(f"  {point_key}: ({x}, {y}) -> ({new_x}, {new_y})")
            self.logger.debug(f"贴图偏移后单元格{i}: {bbox}")

        self.logger.debug("贴图偏移应用完成")
        return updated_annotations

    def _random_crop_with_table(self,
                              composed_image: Image.Image,
                              annotations: Optional[Dict[str, Any]]) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """随机裁剪，确保包含完整表格"""
        if annotations is None or not annotations.get('cells'):
            # 没有标注信息，返回原图
            return composed_image, annotations

        # 计算表格的边界框
        table_bbox = self._calculate_table_bounding_box(annotations)

        # 计算可能的裁剪尺寸范围
        img_width, img_height = composed_image.size
        min_crop_width = max(200, int(table_bbox['width'] * 1.1))  # 至少比表格大10%
        min_crop_height = max(150, int(table_bbox['height'] * 1.1))

        max_crop_width = img_width
        max_crop_height = img_height

        # 随机选择裁剪尺寸
        crop_width = self.random_state.randint(min_crop_width, max_crop_width + 1)
        crop_height = self.random_state.randint(min_crop_height, max_crop_height + 1)

        # 计算安全的裁剪位置范围
        crop_region = self._calculate_safe_crop_region(
            (img_width, img_height), table_bbox, (crop_width, crop_height)
        )

        if crop_region is None:
            # 无法找到安全的裁剪区域，返回原图
            self.logger.warning("无法找到安全的裁剪区域，返回原图")
            return composed_image, annotations

        # 执行裁剪
        crop_x, crop_y = crop_region
        cropped_image = composed_image.crop((
            crop_x, crop_y,
            crop_x + crop_width, crop_y + crop_height
        ))

        # 更新标注坐标
        cropped_annotations = self._update_annotations_for_crop(
            annotations, (crop_x, crop_y)
        )

        return cropped_image, cropped_annotations

    def _calculate_table_bounding_box(self, annotations: Dict[str, Any]) -> Dict[str, float]:
        """计算表格的整体边界框"""
        all_x = []
        all_y = []

        for cell in annotations.get('cells', []):
            bbox = cell.get('bbox', {})
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    all_x.append(x)
                    all_y.append(y)

        if not all_x or not all_y:
            return {'left': 0, 'top': 0, 'right': 100, 'bottom': 100, 'width': 100, 'height': 100}

        left = min(all_x)
        right = max(all_x)
        top = min(all_y)
        bottom = max(all_y)

        return {
            'left': left,
            'top': top,
            'right': right,
            'bottom': bottom,
            'width': right - left,
            'height': bottom - top
        }

    def _calculate_safe_crop_region(self,
                                  image_size: Tuple[int, int],
                                  table_bbox: Dict[str, float],
                                  crop_size: Tuple[int, int]) -> Optional[Tuple[int, int]]:
        """计算安全的裁剪区域，确保表格完全包含在内"""
        img_width, img_height = image_size
        crop_width, crop_height = crop_size

        # 检查裁剪尺寸是否超出图像尺寸
        if crop_width > img_width or crop_height > img_height:
            return None

        # 计算裁剪区域的可能位置范围
        max_crop_x = img_width - crop_width
        max_crop_y = img_height - crop_height

        # 确保表格完全在裁剪区域内的约束
        # 裁剪区域的左边界必须 <= 表格左边界
        # 裁剪区域的右边界必须 >= 表格右边界
        min_crop_x = max(0, int(table_bbox['right']) - crop_width)
        max_crop_x = min(max_crop_x, int(table_bbox['left']))

        min_crop_y = max(0, int(table_bbox['bottom']) - crop_height)
        max_crop_y = min(max_crop_y, int(table_bbox['top']))

        # 检查是否有有效的裁剪区域
        if min_crop_x > max_crop_x or min_crop_y > max_crop_y:
            return None

        # 随机选择裁剪位置
        crop_x = self.random_state.randint(min_crop_x, max_crop_x + 1)
        crop_y = self.random_state.randint(min_crop_y, max_crop_y + 1)

        return (crop_x, crop_y)

    def _update_annotations_for_crop(self,
                                   annotations: Optional[Dict[str, Any]],
                                   crop_offset: Tuple[int, int]) -> Optional[Dict[str, Any]]:
        """更新标注坐标以反映裁剪偏移"""
        if annotations is None:
            self.logger.debug("裁剪偏移: 输入标注为None")
            return None

        self.logger.debug(f"开始应用裁剪偏移，偏移量: {crop_offset}")
        updated_annotations = copy.deepcopy(annotations)
        crop_x, crop_y = crop_offset

        for i, cell in enumerate(updated_annotations.get('cells', [])):
            bbox = cell.get('bbox', {})
            self.logger.debug(f"裁剪偏移前单元格{i}: {bbox}")
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    # 减去裁剪偏移，因为坐标系原点移动了
                    new_x = x - crop_x
                    new_y = y - crop_y

                    # 验证坐标合理性
                    if new_x < 0 or new_y < 0:
                        self.logger.warning(f"裁剪后坐标出现负值: ({new_x}, {new_y})")

                    bbox[point_key] = [new_x, new_y]
                    self.logger.debug(f"  {point_key}: ({x}, {y}) -> ({new_x}, {new_y})")
            self.logger.debug(f"裁剪偏移后单元格{i}: {bbox}")

        self.logger.debug("裁剪偏移应用完成")
        return updated_annotations

    def _smart_crop_with_margin(self, image: Image.Image, annotations: Optional[Dict[str, Any]],
                               margin_config, sample_seed: int) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        V4.2新功能：基于真实表格边界和边距控制的智能裁剪

        Args:
            image: 完整渲染的图像
            annotations: 标注数据（包含真实的表格位置）
            margin_config: 边距控制配置
            sample_seed: 样本种子

        Returns:
            (裁剪后的图像, 更新后的标注)
        """
        if not annotations or 'cells' not in annotations or not annotations['cells']:
            self.logger.warning("智能裁剪：没有有效的标注数据，回退到随机裁剪")
            return self._random_crop_with_table(image, annotations)

        try:
            # 1. 计算表格的真实边界
            table_bounds = self._calculate_table_bounds(annotations)
            self.logger.info(f"[SMART_CROP] 表格真实边界: {table_bounds}")

            # 2. 根据配置选择边距
            margin = self._select_margin_from_config(margin_config, sample_seed)
            self.logger.info(f"[SMART_CROP] 选择边距: {margin}")

            # 3. 计算裁剪区域
            crop_box = self._calculate_crop_box(table_bounds, margin, image.size)
            self.logger.info(f"[SMART_CROP] 裁剪区域: {crop_box}")

            # 4. 裁剪图像
            cropped_image = image.crop(crop_box)

            # 5. 更新标注坐标
            updated_annotations = self._update_annotations_for_crop(annotations, crop_box)

            # 调试：保存智能裁剪的中间结果
            self._save_debug_stage("smart_crop_bounds", image, annotations, {
                "table_bounds": table_bounds,
                "selected_margin": margin,
                "crop_box": crop_box,
                "original_size": image.size,
                "cropped_size": cropped_image.size
            })

            return cropped_image, updated_annotations

        except Exception as e:
            self.logger.error(f"智能裁剪失败: {e}，回退到随机裁剪")
            return self._random_crop_with_table(image, annotations)

    def _calculate_table_bounds(self, annotations: Dict[str, Any]) -> Dict[str, float]:
        """
        计算表格的真实边界

        Args:
            annotations: 标注数据

        Returns:
            {"min_x": float, "min_y": float, "max_x": float, "max_y": float}
        """
        all_x = []
        all_y = []

        for cell in annotations.get('cells', []):
            bbox = cell.get('bbox', {})
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    all_x.append(x)
                    all_y.append(y)

        if not all_x or not all_y:
            raise ValueError("无法从标注数据中提取有效的坐标点")

        bounds = {
            "min_x": min(all_x),
            "min_y": min(all_y),
            "max_x": max(all_x),
            "max_y": max(all_y)
        }

        # 计算表格尺寸用于日志
        table_width = bounds["max_x"] - bounds["min_x"]
        table_height = bounds["max_y"] - bounds["min_y"]
        self.logger.debug(f"表格真实尺寸: {table_width:.1f} x {table_height:.1f}")

        return bounds

    def _select_margin_from_config(self, margin_config, sample_seed: int) -> int:
        """
        根据边距配置选择边距值

        Args:
            margin_config: 边距控制配置
            sample_seed: 样本种子

        Returns:
            选中的边距值
        """
        import numpy as np
        random_state = np.random.RandomState(sample_seed)

        if not margin_config or not hasattr(margin_config, 'range_list') or not margin_config.range_list:
            # 没有配置时使用默认边距
            default_margin = 50
            self.logger.info(f"[MARGIN_SELECT] 使用默认边距: {default_margin}")
            return default_margin

        # 提取范围和概率列表
        range_list = margin_config.range_list
        probability_list = getattr(margin_config, 'probability_list', None)

        if not probability_list or len(probability_list) != len(range_list):
            # 如果没有概率列表或长度不匹配，使用均匀分布
            probability_list = [1.0 / len(range_list)] * len(range_list)
            self.logger.debug("使用均匀概率分布")

        # 归一化概率（支持v3.4风格的自动归一化）
        total_prob = sum(probability_list)
        if total_prob > 0:
            normalized_probabilities = [p / total_prob for p in probability_list]
        else:
            normalized_probabilities = [1.0 / len(range_list)] * len(range_list)

        # 使用加权随机选择边距范围
        selected_index = random_state.choice(len(range_list), p=normalized_probabilities)
        selected_range = range_list[selected_index]

        # 在选中的范围内随机选择具体边距值
        min_margin, max_margin = selected_range
        actual_margin = random_state.randint(min_margin, max_margin + 1)

        self.logger.info(f"[MARGIN_SELECT] 选择范围: {selected_range}, 实际边距: {actual_margin} (概率: {probability_list[selected_index]:.3f})")

        return actual_margin

    def _calculate_crop_box(self, table_bounds: Dict[str, float], margin: int, image_size: Tuple[int, int]) -> Tuple[int, int, int, int]:
        """
        计算裁剪区域

        Args:
            table_bounds: 表格边界
            margin: 边距
            image_size: 图像尺寸 (width, height)

        Returns:
            (left, top, right, bottom) 裁剪框
        """
        image_width, image_height = image_size

        # 基于表格边界和边距计算理想裁剪区域
        ideal_left = int(table_bounds["min_x"] - margin)
        ideal_top = int(table_bounds["min_y"] - margin)
        ideal_right = int(table_bounds["max_x"] + margin)
        ideal_bottom = int(table_bounds["max_y"] + margin)

        # 确保裁剪区域在图像范围内
        crop_left = max(0, ideal_left)
        crop_top = max(0, ideal_top)
        crop_right = min(image_width, ideal_right)
        crop_bottom = min(image_height, ideal_bottom)

        # 验证裁剪区域的有效性
        if crop_right <= crop_left or crop_bottom <= crop_top:
            raise ValueError(f"无效的裁剪区域: ({crop_left}, {crop_top}, {crop_right}, {crop_bottom})")

        # 记录边距调整信息
        actual_left_margin = table_bounds["min_x"] - crop_left
        actual_top_margin = table_bounds["min_y"] - crop_top
        actual_right_margin = crop_right - table_bounds["max_x"]
        actual_bottom_margin = crop_bottom - table_bounds["max_y"]

        self.logger.debug(f"理想裁剪区域: ({ideal_left}, {ideal_top}, {ideal_right}, {ideal_bottom})")
        self.logger.debug(f"实际裁剪区域: ({crop_left}, {crop_top}, {crop_right}, {crop_bottom})")
        self.logger.debug(f"实际边距: 左{actual_left_margin:.1f}, 上{actual_top_margin:.1f}, 右{actual_right_margin:.1f}, 下{actual_bottom_margin:.1f}")

        return (crop_left, crop_top, crop_right, crop_bottom)

    def _update_annotations_for_crop(self, annotations: Optional[Dict[str, Any]], crop_box: Tuple[int, int, int, int]) -> Optional[Dict[str, Any]]:
        """
        更新标注坐标以适应裁剪

        Args:
            annotations: 原始标注数据
            crop_box: 裁剪框 (left, top, right, bottom)

        Returns:
            更新后的标注数据
        """
        if not annotations:
            return annotations

        crop_left, crop_top, crop_right, crop_bottom = crop_box

        # 深拷贝标注数据
        import copy
        updated_annotations = copy.deepcopy(annotations)

        # 更新所有单元格的坐标
        if 'cells' in updated_annotations:
            for cell in updated_annotations['cells']:
                if 'bbox' in cell:
                    bbox = cell['bbox']
                    # 更新四个角点的坐标
                    for point_key in ['p1', 'p2', 'p3', 'p4']:
                        if point_key in bbox:
                            x, y = bbox[point_key]
                            # 减去裁剪偏移
                            new_x = x - crop_left
                            new_y = y - crop_top
                            bbox[point_key] = [new_x, new_y]

        # 更新表格边界框（如果存在）
        if 'table_bbox' in updated_annotations:
            table_bbox = updated_annotations['table_bbox']
            if len(table_bbox) >= 4:
                # 更新表格边界框坐标
                updated_annotations['table_bbox'] = [
                    table_bbox[0] - crop_left,  # left
                    table_bbox[1] - crop_top,   # top
                    table_bbox[2] - crop_left,  # right
                    table_bbox[3] - crop_top    # bottom
                ]

        self.logger.debug(f"标注坐标已更新，裁剪偏移: (-{crop_left}, -{crop_top})")

        return updated_annotations
