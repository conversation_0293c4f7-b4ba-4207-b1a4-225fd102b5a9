"""
性能配置模型

定义TableRender V5.2性能优化相关的配置结构。
"""

import os
from pydantic import BaseModel, Field, field_validator
from typing import Union, Literal


class PerformanceConfig(BaseModel):
    """
    性能配置模型
    
    用于控制TableRender的并行处理和性能优化功能。
    """
    
    enable_parallel: bool = Field(
        default=True, 
        description="是否启用样本级并行处理"
    )
    
    max_workers: Union[Literal["auto"], int] = Field(
        default="auto",
        description="最大工作线程数，'auto'表示自动检测，或指定1-16的具体数值"
    )
    
    max_browser_instances: int = Field(
        default=8,
        ge=1,
        le=16,
        description="最大浏览器实例数，通常等于或小于max_workers"
    )
    
    @field_validator('max_workers')
    @classmethod
    def validate_max_workers(cls, v):
        """验证最大工作线程数配置"""
        if v == "auto":
            return v
        
        if isinstance(v, int):
            if v < 1 or v > 16:
                raise ValueError("max_workers必须在1-16之间，或设置为'auto'")
            return v
        
        raise ValueError("max_workers必须是'auto'或1-16之间的整数")
    
    @field_validator('max_browser_instances')
    @classmethod
    def validate_max_browser_instances(cls, v, info):
        """验证最大浏览器实例数配置"""
        if v < 1 or v > 16:
            raise ValueError("max_browser_instances必须在1-16之间")
        
        # 如果max_workers是具体数值，检查浏览器实例数不应超过工作线程数
        if info.data and 'max_workers' in info.data:
            max_workers = info.data['max_workers']
            if isinstance(max_workers, int) and v > max_workers:
                raise ValueError(f"max_browser_instances({v})不应超过max_workers({max_workers})")
        
        return v
    
    def resolve_max_workers(self) -> int:
        """
        解析最大工作线程数
        
        Returns:
            实际的工作线程数
        """
        if self.max_workers == "auto":
            # 自动检测：使用CPU核心数，但不超过8
            return min(os.cpu_count() or 4, 8)
        return self.max_workers
    
    def get_actual_workers(self, num_samples: int) -> int:
        """
        获取实际使用的工作线程数
        
        Args:
            num_samples: 要生成的样本数量
            
        Returns:
            实际使用的工作线程数（不会超过样本数量）
        """
        if not self.enable_parallel:
            return 1
        
        max_workers = self.resolve_max_workers()
        return min(max_workers, num_samples)
    
    def get_actual_browser_instances(self, actual_workers: int) -> int:
        """
        获取实际使用的浏览器实例数
        
        Args:
            actual_workers: 实际使用的工作线程数
            
        Returns:
            实际使用的浏览器实例数
        """
        return min(self.max_browser_instances, actual_workers)
