#!/usr/bin/env python3
"""
测试V5.2性能配置集成

验证PerformanceConfig是否正确集成到RenderConfig中
"""

import yaml
from pathlib import Path
from pydantic import ValidationError

from table_render.config import RenderConfig


def test_performance_config_integration():
    """测试性能配置集成"""
    print("=== 测试V5.2性能配置集成 ===")
    
    # 测试1: 加载包含performance配置的YAML文件
    print("\n1. 测试加载包含performance配置的YAML文件...")
    try:
        config_path = Path("configs/v5.2_performance_test.yaml")
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return False
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        config = RenderConfig(**config_data)
        print("✅ 配置加载成功")
        
        # 验证性能配置
        if config.performance:
            print(f"   - enable_parallel: {config.performance.enable_parallel}")
            print(f"   - max_workers: {config.performance.max_workers}")
            print(f"   - max_browser_instances: {config.performance.max_browser_instances}")
            print(f"   - 解析后的实际工作线程数: {config.performance.resolve_max_workers()}")
        else:
            print("❌ 性能配置为空")
            return False
            
    except ValidationError as e:
        print(f"❌ 配置验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 加载配置时发生错误: {e}")
        return False
    
    # 测试2: 测试向后兼容性（无performance配置）
    print("\n2. 测试向后兼容性（无performance配置）...")
    try:
        # 创建一个不包含performance配置的最小配置
        minimal_config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 3,
                'cols': 4
            }
        }
        
        config = RenderConfig(**minimal_config_data)
        print("✅ 向后兼容性测试通过")
        
        # 验证默认值
        if config.performance is None:
            print("   - performance配置为None（符合预期）")
        else:
            print(f"❌ performance配置应该为None，但实际为: {config.performance}")
            return False
            
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False
    
    # 测试3: 测试配置验证
    print("\n3. 测试配置验证...")
    try:
        # 测试无效的max_workers值
        invalid_config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 3,
                'cols': 4
            },
            'performance': {
                'enable_parallel': True,
                'max_workers': 20,  # 超出范围
                'max_browser_instances': 8
            }
        }
        
        try:
            config = RenderConfig(**invalid_config_data)
            print("❌ 应该抛出验证错误，但没有")
            return False
        except ValidationError:
            print("✅ 配置验证正常工作（正确拒绝无效配置）")
            
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False
    
    # 测试4: 测试性能配置方法
    print("\n4. 测试性能配置方法...")
    try:
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 3,
                'cols': 4
            },
            'performance': {
                'enable_parallel': True,
                'max_workers': 4,
                'max_browser_instances': 6
            }
        }
        
        config = RenderConfig(**config_data)
        perf_config = config.performance
        
        # 测试get_actual_workers方法
        actual_workers_10 = perf_config.get_actual_workers(10)
        actual_workers_2 = perf_config.get_actual_workers(2)
        
        print(f"   - 10个样本时的实际工作线程数: {actual_workers_10}")
        print(f"   - 2个样本时的实际工作线程数: {actual_workers_2}")
        
        # 测试get_actual_browser_instances方法
        actual_browsers = perf_config.get_actual_browser_instances(actual_workers_10)
        print(f"   - 实际浏览器实例数: {actual_browsers}")
        
        print("✅ 性能配置方法测试通过")
        
    except Exception as e:
        print(f"❌ 性能配置方法测试失败: {e}")
        return False
    
    print("\n=== 所有测试通过 ===")
    return True


if __name__ == "__main__":
    success = test_performance_config_integration()
    if success:
        print("\n🎉 V5.2性能配置集成验证成功！")
    else:
        print("\n💥 V5.2性能配置集成验证失败！")
        exit(1)
