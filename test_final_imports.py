#!/usr/bin/env python3
"""
最终导入测试
"""

import sys
import os
sys.path.insert(0, '.')

def test_final_imports():
    """测试最终导入"""
    print("=== 最终导入测试 ===")
    
    try:
        print("1. 测试基础配置导入...")
        from table_render.config import RenderConfig
        print("   ✅ RenderConfig导入成功")
        
        print("2. 测试TurboJPEG保存器导入...")
        from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
        print("   ✅ TurboJPEGSaver导入成功")
        
        print("3. 测试并行生成器导入...")
        from table_render.utils.parallel_generator import ParallelGenerator
        print("   ✅ ParallelGenerator导入成功")
        
        print("4. 测试主生成器导入...")
        from table_render.main_generator import MainGenerator
        print("   ✅ MainGenerator导入成功")
        
        print("5. 测试配置创建...")
        config_data = {
            'output': {'output_dir': './test_output'},
            'structure': {'header_rows': 1, 'body_rows': 2, 'cols': 3},
            'content': {'data_source': {'type': 'programmatic'}},
            'style': {'common': {'font': {'font_dirs': ['./fonts/']}}},
            'performance': {
                'enable_parallel': True,
                'max_workers': 2,
                'max_browser_instances': 2
            }
        }
        
        config = RenderConfig(**config_data)
        print(f"   ✅ 配置创建成功: parallel={config.performance.enable_parallel}")
        
        print("6. 测试TurboJPEG功能...")
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        stats = saver.get_stats()
        print(f"   ✅ TurboJPEG状态: available={stats['turbo_available']}")
        
        print("7. 测试并行生成器创建...")
        parallel_gen = ParallelGenerator(config, max_workers=2)
        print("   ✅ 并行生成器创建成功")
        
        print("8. 测试主生成器创建...")
        main_gen = MainGenerator(config, debug_mode=False)
        print("   ✅ 主生成器创建成功")
        
        print("\n🎉 所有导入和创建测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_imports()
    sys.exit(0 if success else 1)
