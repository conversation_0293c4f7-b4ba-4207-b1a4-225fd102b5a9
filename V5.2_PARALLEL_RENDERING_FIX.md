# TableRender V5.2 并行渲染质量修复

## 问题描述

在启用`enable_parallel=true`时，TableRender生成的表格图像出现单元格线条消失或部分消失的问题，即使将`max_browser_instances`设置为1也无法解决。

## 根本原因分析

通过深入分析代码，发现问题的根本原因是：

### 1. Playwright实例生命周期管理不当
- **问题**：每次调用`HtmlRenderer.create_async()`都会创建新的Playwright实例，但在`close_async()`中只关闭浏览器，没有关闭Playwright实例
- **影响**：导致资源泄漏和内部状态污染，多个Playwright实例可能共享某些内部状态

### 2. 浏览器渲染引擎的并发竞争
- **问题**：多个线程同时使用Chromium渲染引擎时，CSS的`border-collapse: collapse`计算可能被打断
- **影响**：边框合并计算不完整，导致单元格线条消失

### 3. CSS渲染时序不稳定
- **问题**：在高并发环境下，CSS样式应用和DOM渲染的时序可能不一致
- **影响**：截图时CSS可能尚未完全渲染完成

## 修复方案

### 1. 正确管理Playwright实例生命周期

**修改文件**: `table_render/renderers/html_renderer.py`

#### 修改构造函数
```python
def __init__(self, browser: Browser, playwright_instance, debug_mode: bool = False, debug_output_dir: Optional[str] = None):
    self.browser = browser
    self.playwright_instance = playwright_instance  # V5.2新增：保存Playwright实例引用
    # ... 其他代码
```

#### 修改工厂方法
```python
@staticmethod
async def create_async(debug_mode: bool = False, debug_output_dir: Optional[str] = None) -> 'HtmlRenderer':
    # V5.2修复：为每个渲染器创建独立的Playwright实例
    playwright = await async_playwright().start()
    
    # V5.2修复：使用独立的浏览器启动参数，避免进程间干扰
    browser = await playwright.chromium.launch(
        headless=True,
        args=[
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--single-process',  # V5.2新增：强制单进程模式，避免进程间竞争
        ]
    )
    
    return HtmlRenderer(browser, playwright, debug_mode=debug_mode, debug_output_dir=debug_output_dir)
```

#### 修改关闭方法
```python
async def close_async(self) -> None:
    try:
        # 首先关闭浏览器
        if self.browser:
            await self.browser.close()
    except Exception as e:
        self.logger.warning(f"关闭浏览器时发生错误: {e}")
    
    try:
        # V5.2修复：然后关闭Playwright实例
        if self.playwright_instance:
            await self.playwright_instance.stop()
    except Exception as e:
        self.logger.warning(f"关闭Playwright实例时发生错误: {e}")
```

### 2. 增强CSS渲染稳定性

#### 添加CSS验证方法
```python
async def _validate_css_rendering(self, page) -> bool:
    """验证关键CSS属性是否正确应用"""
    try:
        css_validation = await page.evaluate("""
        () => {
            const table = document.querySelector('#main-table');
            if (!table) return { success: false, error: 'table_not_found' };
            
            const computedStyle = window.getComputedStyle(table);
            const borderCollapse = computedStyle.borderCollapse;
            
            const cells = table.querySelectorAll('td, th');
            if (cells.length === 0) return { success: false, error: 'no_cells_found' };
            
            return {
                success: true,
                borderCollapse: borderCollapse,
                cellCount: cells.length
            };
        }
        """)
        
        return css_validation.get('success', False)
    except Exception as e:
        self.logger.warning(f"CSS验证过程中发生错误: {e}")
        return False
```

#### 添加CSS稳定性保证
```python
async def _ensure_css_stability(self, page) -> None:
    """确保CSS渲染稳定性，特别是边框渲染"""
    try:
        # 强制重新计算样式
        await page.evaluate("""
        () => {
            const table = document.querySelector('#main-table');
            if (table) {
                // 触发重排和重绘
                table.style.visibility = 'hidden';
                table.offsetHeight; // 强制重排
                table.style.visibility = 'visible';
                
                // 确保边框样式正确应用
                const cells = table.querySelectorAll('td, th');
                cells.forEach(cell => {
                    cell.offsetHeight; // 强制每个单元格重排
                });
            }
        }
        """)
        
        await asyncio.sleep(0.2)  # 等待重排完成
    except Exception as e:
        self.logger.warning(f"CSS稳定性检查过程中发生错误: {e}")
```

#### 修改渲染流程
```python
# V5.2修复：增强CSS渲染稳定性，确保边框正确渲染
with profile_stage("css_rendering_stabilization"):
    # 等待DOM完全构建
    await page.wait_for_load_state('domcontentloaded')
    
    # V5.2新增：验证关键CSS属性是否正确应用
    css_validation_passed = await self._validate_css_rendering(page)
    if not css_validation_passed:
        self.logger.warning("CSS渲染验证失败，进行重试")
        # 强制重新渲染CSS
        await page.evaluate("document.body.style.display = 'none'; document.body.offsetHeight; document.body.style.display = 'block';")
        await asyncio.sleep(0.5)

# 背景图加载时增加更长等待时间
if background_params and background_params.background_image_path:
    await page.wait_for_load_state('networkidle')
    await asyncio.sleep(1.5)  # V5.2修复：增加等待时间
else:
    await page.wait_for_load_state('networkidle')
    await asyncio.sleep(1.0)  # V5.2新增：额外等待确保CSS完全渲染

# 截图前最后一次验证CSS状态
await self._ensure_css_stability(page)
```

## 修复效果

### 解决的问题
1. **资源泄漏**：正确关闭Playwright实例，避免内存泄漏
2. **状态污染**：每个线程有完全独立的Playwright和浏览器实例
3. **渲染竞争**：通过单进程模式和CSS稳定性检查避免渲染竞争
4. **时序问题**：增加充分的等待时间和验证机制

### 性能影响
- **资源消耗**：每个线程独立的浏览器实例会增加内存使用
- **渲染时间**：增加的等待时间会略微降低单个样本的生成速度
- **稳定性**：大幅提升并行模式下的渲染质量和稳定性

## 使用建议

1. **服务器资源充足时**：可以放心使用并行模式，享受多线程带来的整体性能提升
2. **资源受限时**：可以适当减少`max_workers`的值，在性能和资源消耗之间找到平衡
3. **调试模式**：启用debug模式可以观察CSS验证和稳定性检查的详细日志

## 版本标识

此修复标记为 **TableRender V5.2**，专门解决并行渲染质量问题。
