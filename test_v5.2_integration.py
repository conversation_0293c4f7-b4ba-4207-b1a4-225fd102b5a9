#!/usr/bin/env python3
"""
TableRender V5.2 集成测试

全面测试V5.2性能优化功能的集成效果，包括：
1. 性能配置加载和验证
2. 并行生成器功能
3. TurboJPEG优化器
4. 异步文件管理器
5. 浏览器实例池
6. 整体性能提升验证
"""

import asyncio
import logging
import os
import time
import yaml
from pathlib import Path
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入TableRender组件
from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator
from table_render.parallel.sample_generator import ParallelSampleGenerator
from table_render.optimizers import TurboJPEGSaver, AsyncFileManager
from table_render.parallel.browser_pool import BrowserPool
from table_render.utils.resource_monitor import ResourceMonitor


class V52IntegrationTester:
    """V5.2集成测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = {}
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.logger.info("=== 开始TableRender V5.2集成测试 ===")
        
        # 测试1: 性能配置测试
        self.test_results['performance_config'] = self.test_performance_config()
        
        # 测试2: TurboJPEG优化器测试
        self.test_results['turbo_jpeg'] = self.test_turbo_jpeg_saver()
        
        # 测试3: 异步文件管理器测试
        self.test_results['async_file_manager'] = asyncio.run(self.test_async_file_manager())
        
        # 测试4: 浏览器实例池测试
        self.test_results['browser_pool'] = asyncio.run(self.test_browser_pool())
        
        # 测试5: 并行生成器测试
        self.test_results['parallel_generator'] = asyncio.run(self.test_parallel_generator())
        
        # 测试6: 主生成器集成测试
        self.test_results['main_generator_integration'] = self.test_main_generator_integration()
        
        # 测试7: 性能对比测试
        self.test_results['performance_comparison'] = self.test_performance_comparison()
        
        # 汇总结果
        self.print_test_summary()
        
        return self.test_results
    
    def test_performance_config(self) -> Dict[str, Any]:
        """测试性能配置功能"""
        self.logger.info("\n1. 测试性能配置功能...")
        
        try:
            # 加载测试配置
            config_path = "configs/v5.2_parallel_complete_test.yaml"
            if not Path(config_path).exists():
                return {'success': False, 'error': f'配置文件不存在: {config_path}'}
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            config = RenderConfig(**config_data)
            
            # 验证性能配置
            if not config.performance:
                return {'success': False, 'error': '性能配置为空'}
            
            perf_config = config.performance
            
            # 测试配置方法
            actual_workers = perf_config.get_actual_workers(10)
            actual_browsers = perf_config.get_actual_browser_instances(actual_workers)
            
            result = {
                'success': True,
                'enable_parallel': perf_config.enable_parallel,
                'max_workers': perf_config.max_workers,
                'resolved_workers': perf_config.resolve_max_workers(),
                'actual_workers_for_10_samples': actual_workers,
                'actual_browsers': actual_browsers
            }
            
            self.logger.info(f"✅ 性能配置测试通过: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 性能配置测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_turbo_jpeg_saver(self) -> Dict[str, Any]:
        """测试TurboJPEG保存器"""
        self.logger.info("\n2. 测试TurboJPEG保存器...")
        
        try:
            from PIL import Image
            import numpy as np
            
            # 创建测试图像
            test_image = Image.new('RGB', (100, 100), color='red')
            
            # 初始化保存器
            saver = TurboJPEGSaver(quality=95, enable_turbo=True)
            
            # 测试保存
            test_path = Path("./test_output/turbo_test.jpg")
            test_path.parent.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            success = saver.save_image(test_image, test_path)
            save_time = time.time() - start_time
            
            # 验证文件存在
            file_exists = test_path.exists()
            
            # 获取性能信息
            perf_info = saver.get_performance_info()
            
            result = {
                'success': success and file_exists,
                'save_time': save_time,
                'performance_info': perf_info,
                'file_size': test_path.stat().st_size if file_exists else 0
            }
            
            # 清理测试文件
            if test_path.exists():
                test_path.unlink()
            
            self.logger.info(f"✅ TurboJPEG测试通过: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ TurboJPEG测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_async_file_manager(self) -> Dict[str, Any]:
        """测试异步文件管理器"""
        self.logger.info("\n3. 测试异步文件管理器...")
        
        try:
            from PIL import Image
            
            # 初始化异步文件管理器
            manager = AsyncFileManager(max_concurrent_writes=4)
            
            # 创建测试数据
            test_image = Image.new('RGB', (50, 50), color='blue')
            test_json = {'test': 'data', 'value': 123}
            test_text = "测试文本内容"
            
            test_dir = Path("./test_output/async_test")
            test_dir.mkdir(parents=True, exist_ok=True)
            
            # 测试异步保存
            start_time = time.time()
            
            tasks = [
                manager.save_image_async(test_image, test_dir / "test.jpg"),
                manager.save_json_async(test_json, test_dir / "test.json"),
                manager.save_text_async(test_text, test_dir / "test.txt")
            ]
            
            results = await asyncio.gather(*tasks)
            save_time = time.time() - start_time
            
            # 验证文件
            files_exist = [
                (test_dir / "test.jpg").exists(),
                (test_dir / "test.json").exists(),
                (test_dir / "test.txt").exists()
            ]
            
            # 获取性能信息
            perf_info = manager.get_performance_info()
            
            # 清理资源
            await manager.close()
            
            result = {
                'success': all(results) and all(files_exist),
                'save_results': results,
                'files_exist': files_exist,
                'save_time': save_time,
                'performance_info': perf_info
            }
            
            # 清理测试文件
            for file_path in [test_dir / "test.jpg", test_dir / "test.json", test_dir / "test.txt"]:
                if file_path.exists():
                    file_path.unlink()
            
            self.logger.info(f"✅ 异步文件管理器测试通过: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 异步文件管理器测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_browser_pool(self) -> Dict[str, Any]:
        """测试浏览器实例池"""
        self.logger.info("\n4. 测试浏览器实例池...")
        
        try:
            # 初始化浏览器实例池
            pool = BrowserPool(max_instances=2)
            
            if not await pool.initialize():
                return {'success': False, 'error': '浏览器实例池初始化失败'}
            
            # 测试HTML渲染
            test_html = "<html><body><h1>测试</h1></body></html>"
            test_css = "body { font-family: Arial; }"
            
            start_time = time.time()
            result_bytes = await pool.render_table_with_pool(test_html, test_css)
            render_time = time.time() - start_time
            
            # 获取统计信息
            stats = pool.get_pool_stats()
            
            # 关闭实例池
            await pool.close()
            
            result = {
                'success': result_bytes is not None,
                'render_time': render_time,
                'result_size': len(result_bytes) if result_bytes else 0,
                'pool_stats': stats
            }
            
            self.logger.info(f"✅ 浏览器实例池测试通过: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器实例池测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_parallel_generator(self) -> Dict[str, Any]:
        """测试并行生成器"""
        self.logger.info("\n5. 测试并行生成器...")
        
        try:
            # 加载配置
            config_path = "configs/v5.2_performance_test.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            config = RenderConfig(**config_data)
            
            # 初始化并行生成器
            generator = ParallelSampleGenerator(config, debug_mode=False)
            
            # 测试生成2个样本
            start_time = time.time()
            result = await generator.generate_samples_parallel(2)
            generation_time = time.time() - start_time
            
            # 获取统计信息
            stats = generator.get_generation_stats()
            
            test_result = {
                'success': result.get('success', False),
                'generation_time': generation_time,
                'result_stats': result.get('stats', {}),
                'generator_stats': stats
            }
            
            self.logger.info(f"✅ 并行生成器测试通过: {test_result}")
            return test_result
            
        except Exception as e:
            self.logger.error(f"❌ 并行生成器测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def test_main_generator_integration(self) -> Dict[str, Any]:
        """测试主生成器集成"""
        self.logger.info("\n6. 测试主生成器集成...")

        try:
            # 测试并行模式
            config_path = "configs/v5.2_performance_test.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            config = RenderConfig(**config_data)

            # 初始化主生成器
            generator = MainGenerator(config, debug_mode=False)

            # 测试生成2个样本
            start_time = time.time()
            generator.generate(2)
            generation_time = time.time() - start_time

            # 验证输出文件
            output_dir = Path(config.output.output_dir)
            generated_files = list(output_dir.glob("sample_*.jpg"))

            result = {
                'success': len(generated_files) >= 2,
                'generation_time': generation_time,
                'generated_files_count': len(generated_files),
                'has_parallel_generator': generator.parallel_generator is not None
            }

            self.logger.info(f"✅ 主生成器集成测试通过: {result}")
            return result

        except Exception as e:
            self.logger.error(f"❌ 主生成器集成测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def test_performance_comparison(self) -> Dict[str, Any]:
        """测试性能对比"""
        self.logger.info("\n7. 测试性能对比...")

        try:
            # 创建串行配置
            serial_config_data = {
                'structure': {
                    'header_rows': 1,
                    'body_rows': 3,
                    'cols': 4
                },
                'performance': {
                    'enable_parallel': False,
                    'max_workers': 1,
                    'max_browser_instances': 1
                }
            }

            # 创建并行配置
            parallel_config_data = {
                'structure': {
                    'header_rows': 1,
                    'body_rows': 3,
                    'cols': 4
                },
                'performance': {
                    'enable_parallel': True,
                    'max_workers': 2,
                    'max_browser_instances': 2
                }
            }

            # 测试样本数量
            num_samples = 4

            # 串行模式测试
            serial_config = RenderConfig(**serial_config_data)
            serial_generator = MainGenerator(serial_config, debug_mode=False)

            start_time = time.time()
            serial_generator.generate(num_samples)
            serial_time = time.time() - start_time

            # 并行模式测试
            parallel_config = RenderConfig(**parallel_config_data)
            parallel_generator = MainGenerator(parallel_config, debug_mode=False)

            start_time = time.time()
            parallel_generator.generate(num_samples)
            parallel_time = time.time() - start_time

            # 计算加速比
            speedup = serial_time / parallel_time if parallel_time > 0 else 0

            result = {
                'success': True,
                'serial_time': serial_time,
                'parallel_time': parallel_time,
                'speedup': speedup,
                'num_samples': num_samples,
                'performance_improvement': f"{speedup:.2f}x" if speedup > 1 else "无提升"
            }

            self.logger.info(f"✅ 性能对比测试完成: {result}")
            return result

        except Exception as e:
            self.logger.error(f"❌ 性能对比测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def print_test_summary(self):
        """打印测试摘要"""
        self.logger.info("\n=== TableRender V5.2集成测试摘要 ===")

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values()
                          if isinstance(result, dict) and result.get('success', False))

        self.logger.info(f"总测试数: {total_tests}")
        self.logger.info(f"通过测试: {passed_tests}")
        self.logger.info(f"失败测试: {total_tests - passed_tests}")
        self.logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")

        # 详细结果
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get('success', False) else "❌ 失败"
            self.logger.info(f"{test_name}: {status}")
            if not result.get('success', False) and 'error' in result:
                self.logger.info(f"  错误: {result['error']}")

        # 性能摘要
        if 'performance_comparison' in self.test_results:
            perf_result = self.test_results['performance_comparison']
            if perf_result.get('success', False):
                self.logger.info(f"\n性能提升: {perf_result.get('performance_improvement', '未知')}")
                self.logger.info(f"串行时间: {perf_result.get('serial_time', 0):.2f}秒")
                self.logger.info(f"并行时间: {perf_result.get('parallel_time', 0):.2f}秒")


def main():
    """主函数"""
    # 确保测试输出目录存在
    Path("./test_output").mkdir(parents=True, exist_ok=True)

    # 运行集成测试
    tester = V52IntegrationTester()
    results = tester.run_all_tests()

    # 检查整体结果
    overall_success = all(
        result.get('success', False)
        for result in results.values()
    )

    if overall_success:
        logger.info("\n🎉 所有V5.2集成测试通过！")
        exit(0)
    else:
        logger.error("\n💥 部分V5.2集成测试失败！")
        exit(1)


if __name__ == "__main__":
    main()
