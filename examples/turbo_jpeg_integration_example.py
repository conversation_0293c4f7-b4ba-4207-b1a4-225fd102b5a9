#!/usr/bin/env python3
"""
TurboJPEG集成示例

展示如何在现有的TableRender代码中集成TurboJPEG优化器。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from table_render.optimizers import TurboJPEGSaver
from table_render.utils.file_utils import FileUtils


class OptimizedFileUtils:
    """
    优化的文件工具类示例
    
    展示如何集成TurboJPEG优化器到现有的文件保存流程中。
    """
    
    def __init__(self, use_turbo_jpeg: bool = True, jpeg_quality: int = 95):
        """
        初始化优化的文件工具
        
        Args:
            use_turbo_jpeg: 是否使用TurboJPEG优化
            jpeg_quality: JPEG质量参数
        """
        self.turbo_saver = TurboJPEGSaver(
            quality=jpeg_quality, 
            enable_turbo=use_turbo_jpeg
        ) if use_turbo_jpeg else None
    
    def save_image_optimized(self, image_bytes: bytes, output_path: str, 
                           use_jpeg: bool = False) -> None:
        """
        优化的图像保存方法
        
        Args:
            image_bytes: 图像的二进制数据
            output_path: 输出文件路径
            use_jpeg: 是否转换为JPEG格式以提升速度
        """
        if self.turbo_saver and use_jpeg:
            # 使用TurboJPEG优化保存
            # 将PNG路径改为JPEG路径
            output_path = output_path.replace('.png', '.jpg')
            success = self.turbo_saver.save_image_bytes(
                image_bytes, output_path, format_hint="jpeg"
            )
            if not success:
                # 回退到原始方法
                FileUtils.save_image(image_bytes, output_path)
        else:
            # 使用原始方法
            FileUtils.save_image(image_bytes, output_path)
    
    def save_sample_optimized(
        self,
        sample_index: int,
        image_bytes: bytes,
        annotations: dict,
        metadata: dict,
        output_dirs: dict,
        label_suffix: str = None,
        use_jpeg_optimization: bool = True
    ) -> None:
        """
        优化的样本保存方法
        
        Args:
            sample_index: 样本索引
            image_bytes: 图像数据
            annotations: 标注数据
            metadata: 元数据
            output_dirs: 输出目录字典
            label_suffix: 标注文件后缀
            use_jpeg_optimization: 是否使用JPEG优化
        """
        # 生成文件名
        base_filename = f"{sample_index:06d}"
        
        # 保存图像（可选择使用JPEG优化）
        if use_jpeg_optimization and self.turbo_saver:
            image_path = os.path.join(output_dirs['images'], f"{base_filename}.jpg")
        else:
            image_path = os.path.join(output_dirs['images'], f"{base_filename}.png")
        
        self.save_image_optimized(image_bytes, image_path, use_jpeg_optimization)
        
        # 保存标注（保持原有逻辑）
        if label_suffix:
            annotation_filename = f"{base_filename}{label_suffix}.json"
        else:
            annotation_filename = f"{base_filename}.json"
        annotation_path = os.path.join(output_dirs['annotations'], annotation_filename)
        FileUtils.save_json(annotations, annotation_path)
        
        # 保存元数据（保持原有逻辑）
        metadata_path = os.path.join(output_dirs['metadata'], f"{base_filename}.json")
        FileUtils.save_json(metadata, metadata_path)


def demonstrate_integration():
    """演示集成示例"""
    print("=== TurboJPEG集成示例 ===")
    
    # 创建优化的文件工具
    optimized_utils = OptimizedFileUtils(use_turbo_jpeg=True, jpeg_quality=95)
    
    # 显示TurboJPEG状态
    if optimized_utils.turbo_saver:
        stats = optimized_utils.turbo_saver.get_stats()
        print(f"TurboJPEG状态: {stats}")
    
    # 模拟保存过程
    print("\n模拟保存过程:")
    print("1. 在MainGenerator中，可以这样使用:")
    print("   optimized_utils.save_sample_optimized(..., use_jpeg_optimization=True)")
    print("2. 这将自动使用TurboJPEG加速JPEG保存")
    print("3. 如果TurboJPEG不可用，会自动回退到PIL")
    
    print("\n集成建议:")
    print("- 在MainGenerator.__init__()中创建OptimizedFileUtils实例")
    print("- 在保存样本时调用save_sample_optimized()方法")
    print("- 可以通过配置控制是否启用JPEG优化")


def show_performance_benefits():
    """展示性能优势"""
    print("\n=== 性能优势 ===")
    print("TurboJPEG优化器的主要优势:")
    print("1. 速度提升: 相比PIL提升3-5倍JPEG编码速度")
    print("2. 优雅降级: TurboJPEG不可用时自动使用PIL")
    print("3. 智能格式: 自动选择最优的图像格式")
    print("4. 兼容性: 完全兼容现有的字节数据接口")
    print("5. 配置灵活: 支持质量参数和格式提示")
    
    print("\n预期效果:")
    print("- 单样本图像保存时间: 7.8秒 → 1.5-2.5秒")
    print("- 20个样本总节省时间: 约100-126秒")
    print("- 与样本级并行结合: 可获得叠加的性能提升")


if __name__ == "__main__":
    demonstrate_integration()
    show_performance_benefits()
