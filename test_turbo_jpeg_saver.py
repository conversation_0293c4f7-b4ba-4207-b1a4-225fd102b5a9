#!/usr/bin/env python3
"""
TurboJPEG保存器测试脚本

用于验证TurboJPEG保存器的功能和性能。
"""

import os
import time
import tempfile
from pathlib import Path
from PIL import Image
import numpy as np

# 添加项目路径
import sys
sys.path.insert(0, '.')

from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
from table_render.utils.file_utils import FileUtils


def create_test_image(width: int = 800, height: int = 600) -> Image.Image:
    """创建测试图像"""
    # 创建一个彩色测试图像
    image_array = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
    
    # 添加一些结构化内容（模拟表格）
    # 添加网格线
    for i in range(0, height, 50):
        image_array[i:i+2, :] = [0, 0, 0]  # 水平线
    for j in range(0, width, 100):
        image_array[:, j:j+2] = [0, 0, 0]  # 垂直线
    
    # 添加一些彩色块
    for i in range(5):
        for j in range(8):
            y_start = i * 50 + 10
            y_end = y_start + 30
            x_start = j * 100 + 10
            x_end = x_start + 80
            
            if y_end < height and x_end < width:
                color = [
                    np.random.randint(100, 256),
                    np.random.randint(100, 256),
                    np.random.randint(100, 256)
                ]
                image_array[y_start:y_end, x_start:x_end] = color
    
    return Image.fromarray(image_array)


def test_turbo_jpeg_saver():
    """测试TurboJPEG保存器"""
    print("=== TurboJPEG保存器测试 ===")
    
    # 创建测试图像
    print("创建测试图像...")
    test_image = create_test_image(1200, 800)
    
    # 转换为字节数据
    import io
    buffer = io.BytesIO()
    test_image.save(buffer, format='PNG')
    image_bytes = buffer.getvalue()
    print(f"测试图像大小: {len(image_bytes)} 字节")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试1: TurboJPEG保存器直接测试
        print("\n--- 测试1: TurboJPEG保存器直接测试 ---")
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        print(f"TurboJPEG可用: {saver.turbo_available}")
        print(f"保存器统计: {saver.get_stats()}")
        
        # 测试保存PIL图像
        jpeg_path = temp_path / "test_direct.jpg"
        png_path = temp_path / "test_direct.png"
        
        start_time = time.time()
        success_jpeg = saver.save_image(test_image, jpeg_path, format_hint="jpeg")
        jpeg_time = time.time() - start_time
        
        start_time = time.time()
        success_png = saver.save_image(test_image, png_path, format_hint="png")
        png_time = time.time() - start_time
        
        print(f"JPEG保存: {'成功' if success_jpeg else '失败'}, 耗时: {jpeg_time*1000:.2f}ms")
        print(f"PNG保存: {'成功' if success_png else '失败'}, 耗时: {png_time*1000:.2f}ms")
        
        if jpeg_path.exists():
            print(f"JPEG文件大小: {jpeg_path.stat().st_size} 字节")
        if png_path.exists():
            print(f"PNG文件大小: {png_path.stat().st_size} 字节")
        
        # 测试保存字节数据
        jpeg_bytes_path = temp_path / "test_bytes.jpg"
        png_bytes_path = temp_path / "test_bytes.png"
        
        start_time = time.time()
        success_jpeg_bytes = saver.save_image_bytes(image_bytes, jpeg_bytes_path, format_hint="jpeg")
        jpeg_bytes_time = time.time() - start_time
        
        start_time = time.time()
        success_png_bytes = saver.save_image_bytes(image_bytes, png_bytes_path, format_hint="png")
        png_bytes_time = time.time() - start_time
        
        print(f"JPEG字节保存: {'成功' if success_jpeg_bytes else '失败'}, 耗时: {jpeg_bytes_time*1000:.2f}ms")
        print(f"PNG字节保存: {'成功' if success_png_bytes else '失败'}, 耗时: {png_bytes_time*1000:.2f}ms")
        
        # 测试2: FileUtils集成测试
        print("\n--- 测试2: FileUtils集成测试 ---")
        
        # 测试原始方法
        original_path = temp_path / "test_original.png"
        start_time = time.time()
        FileUtils.save_image(image_bytes, str(original_path))
        original_time = time.time() - start_time
        print(f"原始方法保存: 耗时: {original_time*1000:.2f}ms")
        
        # 测试优化方法（PNG）
        optimized_png_path = temp_path / "test_optimized.png"
        start_time = time.time()
        FileUtils.save_image_optimized(image_bytes, str(optimized_png_path), use_turbo=True, format_hint="png")
        optimized_png_time = time.time() - start_time
        print(f"优化方法保存(PNG): 耗时: {optimized_png_time*1000:.2f}ms")
        
        # 测试优化方法（JPEG）
        optimized_jpeg_path = temp_path / "test_optimized.jpg"
        start_time = time.time()
        FileUtils.save_image_optimized(image_bytes, str(optimized_jpeg_path), use_turbo=True, format_hint="jpeg")
        optimized_jpeg_time = time.time() - start_time
        print(f"优化方法保存(JPEG): 耗时: {optimized_jpeg_time*1000:.2f}ms")
        
        # 性能对比
        print("\n--- 性能对比 ---")
        if saver.turbo_available:
            speedup_jpeg = original_time / optimized_jpeg_time if optimized_jpeg_time > 0 else 0
            print(f"JPEG加速比: {speedup_jpeg:.2f}x")
        
        # 测试3: save_sample集成测试
        print("\n--- 测试3: save_sample集成测试 ---")
        
        # 创建输出目录结构
        output_dirs = FileUtils.ensure_output_dirs(str(temp_path / "sample_test"))
        
        # 创建测试数据
        test_annotations = {
            "cells": [
                {"id": "cell_0_0", "polygon": [[10, 10], [100, 10], [100, 50], [10, 50]]},
                {"id": "cell_0_1", "polygon": [[110, 10], [200, 10], [200, 50], [110, 50]]}
            ]
        }
        test_metadata = {"sample_index": 0, "timestamp": "2025-01-01T00:00:00"}
        
        # 测试原始save_sample
        start_time = time.time()
        FileUtils.save_sample(0, image_bytes, test_annotations, test_metadata, output_dirs)
        original_sample_time = time.time() - start_time
        print(f"原始save_sample: 耗时: {original_sample_time*1000:.2f}ms")
        
        # 测试优化save_sample
        start_time = time.time()
        FileUtils.save_sample(1, image_bytes, test_annotations, test_metadata, output_dirs, 
                            use_turbo=True, format_hint="jpeg")
        optimized_sample_time = time.time() - start_time
        print(f"优化save_sample: 耗时: {optimized_sample_time*1000:.2f}ms")
        
        # 检查文件是否存在
        original_image_path = Path(output_dirs['images']) / "000000.png"
        optimized_image_path = Path(output_dirs['images']) / "000001.png"  # 注意：仍然是.png扩展名
        
        print(f"原始图像文件存在: {original_image_path.exists()}")
        print(f"优化图像文件存在: {optimized_image_path.exists()}")
        
        if original_image_path.exists() and optimized_image_path.exists():
            print(f"原始图像大小: {original_image_path.stat().st_size} 字节")
            print(f"优化图像大小: {optimized_image_path.stat().st_size} 字节")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_turbo_jpeg_saver()
