#!/usr/bin/env python3
"""
测试导入修复

验证所有模块导入是否正常工作
"""

def test_imports():
    """测试所有关键导入"""
    print("测试导入修复...")
    
    try:
        # 测试主模块导入（现在使用绝对导入）
        print("1. 测试主模块导入...")
        from table_render import RenderConfig, ResolvedParams, PostprocessingConfig, TableBlendingConfig
        print("   ✅ 主模块导入成功")

        # 测试直接从config.py导入（应该避免使用）
        print("2. 测试config.py文件导入...")
        import importlib.util
        from pathlib import Path
        config_file = Path("table_render/config.py")
        spec = importlib.util.spec_from_file_location("config_test", config_file)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        print("   ✅ config.py文件导入成功")
        
        # 测试并行模块导入
        print("3. 测试并行模块导入...")
        from table_render.parallel import BrowserPool, ParallelSampleGenerator
        print("   ✅ 并行模块导入成功")
        
        # 测试优化器模块导入
        print("4. 测试优化器模块导入...")
        from table_render.optimizers import TurboJPEGSaver, AsyncFileManager
        print("   ✅ 优化器模块导入成功")
        
        # 测试主生成器导入
        print("5. 测试主生成器导入...")
        from table_render.main_generator import MainGenerator
        print("   ✅ 主生成器导入成功")
        
        # 测试性能配置实例化
        print("6. 测试性能配置实例化...")
        PerformanceConfig = config_module.PerformanceConfig
        perf_config = PerformanceConfig()
        print(f"   ✅ 性能配置实例化成功: enable_parallel={perf_config.enable_parallel}")
        
        # 测试配置验证
        print("7. 测试配置验证...")
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 3,
                'cols': 4
            },
            'performance': {
                'enable_parallel': True,
                'max_workers': 4,
                'max_browser_instances': 4
            }
        }
        config = RenderConfig(**config_data)
        print(f"   ✅ 配置验证成功: performance.enable_parallel={config.performance.enable_parallel}")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False


if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n✅ 导入修复验证成功！")
        exit(0)
    else:
        print("\n❌ 导入修复验证失败！")
        exit(1)
