#!/usr/bin/env python3
"""
最终导入测试

验证所有修复后的导入是否正常工作
"""

def test_all_imports():
    """测试所有关键模块的导入"""
    print("=== 最终导入测试 ===")
    
    try:
        # 测试1: 主模块导入
        print("1. 测试主模块导入...")
        from table_render import RenderConfig, ResolvedParams, PostprocessingConfig, TableBlendingConfig
        print("   ✅ 主模块导入成功")
        
        # 测试2: 主生成器导入
        print("2. 测试主生成器导入...")
        from table_render.main_generator import MainGenerator
        print("   ✅ 主生成器导入成功")
        
        # 测试3: 解析器导入
        print("3. 测试解析器导入...")
        from table_render.resolver import Resolver
        print("   ✅ 解析器导入成功")
        
        # 测试4: 构建器导入
        print("4. 测试构建器导入...")
        from table_render.builders.structure_builder import StructureBuilder
        from table_render.builders.content_builder import ContentBuilder
        from table_render.builders.style_builder import StyleBuilder
        print("   ✅ 构建器导入成功")
        
        # 测试5: 后处理器导入
        print("5. 测试后处理器导入...")
        from table_render.postprocessors.image_augmentor import ImageAugmentor
        from table_render.postprocessors.base_augmentor import BaseAugmentor
        from table_render.postprocessors.degradation_processor import DegradationProcessor
        print("   ✅ 后处理器导入成功")
        
        # 测试6: 并行模块导入
        print("6. 测试并行模块导入...")
        from table_render.parallel import BrowserPool, ParallelSampleGenerator
        print("   ✅ 并行模块导入成功")
        
        # 测试7: 优化器模块导入
        print("7. 测试优化器模块导入...")
        from table_render.optimizers import TurboJPEGSaver, AsyncFileManager
        print("   ✅ 优化器模块导入成功")
        
        # 测试8: 工具模块导入
        print("8. 测试工具模块导入...")
        from table_render.utils.style_utils import StyleInheritanceManager
        print("   ✅ 工具模块导入成功")
        
        # 测试9: 配置实例化
        print("9. 测试配置实例化...")
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 3,
                'cols': 4
            },
            'performance': {
                'enable_parallel': True,
                'max_workers': 4,
                'max_browser_instances': 4
            }
        }
        config = RenderConfig(**config_data)
        print(f"   ✅ 配置实例化成功: performance.enable_parallel={config.performance.enable_parallel}")
        
        # 测试10: 主生成器实例化
        print("10. 测试主生成器实例化...")
        generator = MainGenerator(config, debug_mode=False)
        print(f"   ✅ 主生成器实例化成功: has_parallel_generator={generator.parallel_generator is not None}")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_directory_status():
    """检查config目录状态"""
    print("\n=== 配置目录状态检查 ===")
    
    from pathlib import Path
    
    config_dir = Path("table_render/config")
    config_file = Path("table_render/config.py")
    
    print(f"config目录存在: {config_dir.exists()}")
    if config_dir.exists():
        files = list(config_dir.iterdir())
        print(f"config目录内容: {[f.name for f in files]}")
    
    print(f"config.py文件存在: {config_file.exists()}")
    
    # 检查Python模块解析
    try:
        import table_render.config
        print(f"table_render.config解析为: {table_render.config.__file__}")
    except Exception as e:
        print(f"table_render.config解析失败: {e}")


if __name__ == "__main__":
    # 检查配置目录状态
    test_config_directory_status()
    
    # 运行导入测试
    success = test_all_imports()
    
    if success:
        print("\n✅ 最终导入测试验证成功！")
        print("现在可以正常运行TableRender了:")
        print("python -m table_render.main configs/v5_complete.yaml --num-samples 20 --debug")
        exit(0)
    else:
        print("\n❌ 最终导入测试验证失败！")
        exit(1)
