#!/usr/bin/env python3
"""
测试图像类型处理修复

验证PIL图像对象和字节数据的正确处理。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_image_augmentor_return_type():
    """测试ImageAugmentor返回类型处理"""
    print("=== 测试ImageAugmentor返回类型处理 ===")
    
    try:
        from table_render.postprocessors.image_augmentor import ImageAugmentor
        from PIL import Image
        import io
        
        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (100, 100), color='white')
        buffer = io.BytesIO()
        test_image.save(buffer, format='PNG')
        test_image_bytes = buffer.getvalue()
        
        print(f"✅ 测试图像创建成功，大小: {len(test_image_bytes)} bytes")
        
        # 创建ImageAugmentor实例
        augmentor = ImageAugmentor(42)
        
        # 测试无后处理参数的情况
        result_image, result_annotations = augmentor.process(test_image_bytes, None, None)
        
        if isinstance(result_image, bytes):
            print("✅ 无后处理参数时返回字节数据")
        else:
            print(f"❌ 无后处理参数时返回了 {type(result_image)} 而不是字节数据")
            return False
        
        # 测试有后处理参数的情况
        from table_render.config import ResolvedPostprocessingParams
        
        # 创建简单的后处理参数
        postprocessing_params = ResolvedPostprocessingParams(
            apply_perspective=False,
            apply_background=False,
            apply_degradation_blur=False
        )
        
        result_image2, result_annotations2 = augmentor.process(
            test_image_bytes, None, postprocessing_params
        )
        
        print(f"有后处理参数时返回类型: {type(result_image2)}")
        
        # 检查返回类型
        if isinstance(result_image2, bytes):
            print("✅ 有后处理参数时返回字节数据")
        elif hasattr(result_image2, 'save'):  # PIL图像对象
            print("⚠️  有后处理参数时返回PIL图像对象（需要在调用方转换）")
        else:
            print(f"❌ 有后处理参数时返回了未知类型: {type(result_image2)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ImageAugmentor测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


def test_serial_generation_with_postprocessing():
    """测试串行生成中的图像类型处理"""
    print("\n=== 测试串行生成中的图像类型处理 ===")
    
    # 创建包含后处理的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_perspective': False,  # 禁用透视变换以简化测试
            'apply_background': False,   # 禁用背景以简化测试
            'apply_degradation_blur': True,  # 启用模糊降质
            'degradation_blur_kernel_size': [1, 2],
            'degradation_blur_sigma': [0.5, 1.0]
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 串行MainGenerator创建成功")
            
            # 测试单个样本生成
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成（包含后处理）...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 图像大小: {result['image_size_bytes']} bytes")
            
            return True
            
        except TypeError as e:
            if "object of type 'PngImageFile' has no len()" in str(e):
                print(f"❌ 仍然存在PIL图像对象len()错误: {e}")
                return False
            else:
                print(f"❌ 其他TypeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_generation_with_postprocessing():
    """测试并行生成中的图像类型处理"""
    print("\n=== 测试并行生成中的图像类型处理 ===")
    
    # 创建包含后处理的并行测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_perspective': False,  # 禁用透视变换以简化测试
            'apply_background': False,   # 禁用背景以简化测试
            'apply_degradation_noise': True,  # 启用噪声降质
            'degradation_noise_intensity': [0.01, 0.02]
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行MainGenerator创建成功")
            
            # 测试并行生成
            print("开始测试并行生成（包含后处理）...")
            generator.generate(3)  # 生成3个样本
            
            print("✅ 并行生成成功")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return len(image_files) == 3 and len(annotation_files) == 3
            else:
                print("❌ 输出目录不存在")
                return False
            
        except TypeError as e:
            if "object of type 'PngImageFile' has no len()" in str(e):
                print(f"❌ 并行模式仍然存在PIL图像对象len()错误: {e}")
                return False
            else:
                print(f"❌ 其他TypeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 并行测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数"""
    print("TableRender V5.2 图像类型处理修复测试")
    print("=" * 50)
    
    # 测试ImageAugmentor返回类型
    augmentor_success = test_image_augmentor_return_type()
    
    # 测试串行生成
    serial_success = test_serial_generation_with_postprocessing()
    
    # 测试并行生成
    parallel_success = test_parallel_generation_with_postprocessing()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"ImageAugmentor类型处理: {'✅ 通过' if augmentor_success else '❌ 失败'}")
    print(f"串行生成测试: {'✅ 通过' if serial_success else '❌ 失败'}")
    print(f"并行生成测试: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = augmentor_success and serial_success and parallel_success
    
    if all_success:
        print("\n🎉 所有测试通过！图像类型处理问题已修复")
        print("现在可以正常使用 TableRender V5.2 的所有功能")
        
        print("\n📋 修复总结:")
        print("1. ✅ 处理ImageAugmentor返回PIL图像对象的情况")
        print("2. ✅ 在调用方正确转换PIL图像为字节数据")
        print("3. ✅ 避免对PIL图像对象使用len()函数")
        print("4. ✅ 串行和并行模式都能正常处理图像类型")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
