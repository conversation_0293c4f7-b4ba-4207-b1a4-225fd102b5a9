#!/usr/bin/env python3
"""
TableRender V5.2 性能测试脚本

测试并行生成和TurboJPEG优化的性能提升效果。
"""

import os
import sys
import time
import yaml
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('v5.2_performance_test.log')
        ]
    )


def load_config(config_path: str) -> RenderConfig:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    return RenderConfig(**config_data)


def run_performance_test(config_path: str, num_samples: int, test_name: str) -> dict:
    """运行性能测试"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"开始 {test_name} 测试...")
    logger.info(f"配置文件: {config_path}")
    logger.info(f"样本数量: {num_samples}")
    
    try:
        # 加载配置
        config = load_config(config_path)
        
        # 创建生成器
        generator = MainGenerator(config, debug_mode=False)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行生成
        generator.generate(num_samples)
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        # 计算统计信息
        avg_time_per_sample = total_time / num_samples if num_samples > 0 else 0
        
        result = {
            'test_name': test_name,
            'config_path': config_path,
            'num_samples': num_samples,
            'total_time': total_time,
            'avg_time_per_sample': avg_time_per_sample,
            'success': True,
            'parallel_enabled': config.performance.enable_parallel if config.performance else False,
            'max_workers': config.performance.resolve_max_workers() if config.performance else 1
        }
        
        logger.info(f"{test_name} 测试完成!")
        logger.info(f"总耗时: {total_time:.2f}秒")
        logger.info(f"平均耗时: {avg_time_per_sample:.2f}秒/样本")
        
        return result
        
    except Exception as e:
        logger.error(f"{test_name} 测试失败: {e}")
        return {
            'test_name': test_name,
            'config_path': config_path,
            'num_samples': num_samples,
            'success': False,
            'error': str(e)
        }


def compare_performance(serial_result: dict, parallel_result: dict):
    """对比性能结果"""
    logger = logging.getLogger(__name__)
    
    if not (serial_result['success'] and parallel_result['success']):
        logger.error("无法对比性能：部分测试失败")
        return
    
    serial_time = serial_result['total_time']
    parallel_time = parallel_result['total_time']
    
    if parallel_time > 0:
        speedup = serial_time / parallel_time
        time_saved = serial_time - parallel_time
        
        logger.info("=" * 60)
        logger.info("性能对比结果")
        logger.info("=" * 60)
        logger.info(f"串行模式总耗时: {serial_time:.2f}秒")
        logger.info(f"并行模式总耗时: {parallel_time:.2f}秒")
        logger.info(f"加速比: {speedup:.2f}x")
        logger.info(f"节省时间: {time_saved:.2f}秒")
        logger.info(f"并行线程数: {parallel_result['max_workers']}")
        
        # 判断是否达到预期
        expected_speedup = 1.5  # 预期至少1.5倍加速
        if speedup >= expected_speedup:
            logger.info(f"✅ 性能提升达到预期 (>= {expected_speedup}x)")
        else:
            logger.warning(f"⚠️ 性能提升未达到预期 (< {expected_speedup}x)")
        
        return {
            'serial_time': serial_time,
            'parallel_time': parallel_time,
            'speedup': speedup,
            'time_saved': time_saved,
            'meets_expectation': speedup >= expected_speedup
        }
    else:
        logger.error("并行测试时间为0，无法计算加速比")
        return None


def test_turbo_jpeg_integration():
    """测试TurboJPEG集成"""
    logger = logging.getLogger(__name__)
    
    logger.info("测试TurboJPEG集成...")
    
    try:
        from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
        
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        stats = saver.get_stats()
        
        logger.info(f"TurboJPEG可用: {stats['turbo_available']}")
        logger.info(f"JPEG质量: {stats['quality']}")
        logger.info(f"TurboJPEG启用: {stats['enable_turbo']}")
        
        return stats['turbo_available']
        
    except Exception as e:
        logger.error(f"TurboJPEG测试失败: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("TableRender V5.2 性能测试开始")
    
    # 测试参数
    num_samples = 4  # 使用较小的样本数进行快速测试
    
    # 测试TurboJPEG集成
    turbo_available = test_turbo_jpeg_integration()
    
    # 运行串行测试
    serial_result = run_performance_test(
        "configs/v5.2_test_serial.yaml",
        num_samples,
        "串行模式"
    )
    
    # 运行并行测试
    parallel_result = run_performance_test(
        "configs/v5.2_test_parallel.yaml", 
        num_samples,
        "并行模式"
    )
    
    # 对比性能
    comparison = compare_performance(serial_result, parallel_result)
    
    # 生成测试报告
    report = {
        'test_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
        'num_samples': num_samples,
        'turbo_jpeg_available': turbo_available,
        'serial_result': serial_result,
        'parallel_result': parallel_result,
        'comparison': comparison
    }
    
    # 保存测试报告
    report_path = "v5.2_performance_test_report.yaml"
    with open(report_path, 'w', encoding='utf-8') as f:
        yaml.dump(report, f, default_flow_style=False, allow_unicode=True)
    
    logger.info(f"测试报告已保存: {report_path}")
    
    # 输出最终结果
    if comparison and comparison['meets_expectation']:
        logger.info("🎉 V5.2性能优化验证成功！")
        return 0
    else:
        logger.warning("⚠️ V5.2性能优化效果未达到预期")
        return 1


if __name__ == "__main__":
    exit(main())
