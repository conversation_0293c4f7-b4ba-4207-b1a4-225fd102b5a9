#!/usr/bin/env python3
"""
测试TurboJPEG配置功能

验证通过配置文件控制TurboJPEG的功能是否正常工作。
"""

import sys
import tempfile
from pathlib import Path
import time

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_turbo_jpeg_config_loading():
    """测试TurboJPEG配置加载"""
    print("=== 测试TurboJPEG配置加载 ===")
    
    # 测试启用TurboJPEG的配置
    config_data_enabled = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3
        },
        'performance': {
            'enable_parallel': False,
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 98,
            'turbo_jpeg_format': 'jpeg'
        }
    }
    
    try:
        config = RenderConfig(**config_data_enabled)
        print("✅ 启用TurboJPEG的配置加载成功")
        print(f"   - enable_turbo_jpeg: {config.performance.enable_turbo_jpeg}")
        print(f"   - turbo_jpeg_quality: {config.performance.turbo_jpeg_quality}")
        print(f"   - turbo_jpeg_format: {config.performance.turbo_jpeg_format}")
        
        # 测试禁用TurboJPEG的配置
        config_data_disabled = config_data_enabled.copy()
        config_data_disabled['performance']['enable_turbo_jpeg'] = False
        
        config_disabled = RenderConfig(**config_data_disabled)
        print("✅ 禁用TurboJPEG的配置加载成功")
        print(f"   - enable_turbo_jpeg: {config_disabled.performance.enable_turbo_jpeg}")
        
        return True
        
    except Exception as e:
        print(f"❌ TurboJPEG配置加载失败: {e}")
        return False


def test_turbo_jpeg_quality_validation():
    """测试TurboJPEG质量参数验证"""
    print("\n=== 测试TurboJPEG质量参数验证 ===")
    
    base_config = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3
        },
        'performance': {
            'enable_turbo_jpeg': True,
            'turbo_jpeg_format': 'jpeg'
        }
    }
    
    # 测试有效的质量参数
    valid_qualities = [1, 50, 95, 98, 100]
    for quality in valid_qualities:
        try:
            config_data = base_config.copy()
            config_data['performance']['turbo_jpeg_quality'] = quality
            config = RenderConfig(**config_data)
            print(f"✅ 质量参数 {quality} 验证通过")
        except Exception as e:
            print(f"❌ 质量参数 {quality} 验证失败: {e}")
            return False
    
    # 测试无效的质量参数
    invalid_qualities = [0, -1, 101, 200]
    for quality in invalid_qualities:
        try:
            config_data = base_config.copy()
            config_data['performance']['turbo_jpeg_quality'] = quality
            config = RenderConfig(**config_data)
            print(f"❌ 质量参数 {quality} 应该验证失败但通过了")
            return False
        except Exception:
            print(f"✅ 质量参数 {quality} 正确验证失败")
    
    return True


def test_turbo_jpeg_format_validation():
    """测试TurboJPEG格式参数验证"""
    print("\n=== 测试TurboJPEG格式参数验证 ===")
    
    base_config = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3
        },
        'performance': {
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 95
        }
    }
    
    # 测试有效的格式参数
    valid_formats = ["jpeg", "png", "auto", "JPEG", "PNG", "AUTO"]
    for format_hint in valid_formats:
        try:
            config_data = base_config.copy()
            config_data['performance']['turbo_jpeg_format'] = format_hint
            config = RenderConfig(**config_data)
            print(f"✅ 格式参数 '{format_hint}' 验证通过，转换为: '{config.performance.turbo_jpeg_format}'")
        except Exception as e:
            print(f"❌ 格式参数 '{format_hint}' 验证失败: {e}")
            return False
    
    # 测试无效的格式参数
    invalid_formats = ["jpg", "gif", "bmp", "invalid"]
    for format_hint in invalid_formats:
        try:
            config_data = base_config.copy()
            config_data['performance']['turbo_jpeg_format'] = format_hint
            config = RenderConfig(**config_data)
            print(f"❌ 格式参数 '{format_hint}' 应该验证失败但通过了")
            return False
        except Exception:
            print(f"✅ 格式参数 '{format_hint}' 正确验证失败")
    
    return True


def test_turbo_jpeg_enabled_generation():
    """测试启用TurboJPEG的生成"""
    print("\n=== 测试启用TurboJPEG的生成 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式便于测试
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 98,
            'turbo_jpeg_format': 'jpeg'
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 启用TurboJPEG的MainGenerator创建成功")
            
            # 测试生成时间
            start_time = time.time()
            generator.generate(2)  # 生成2个样本
            end_time = time.time()
            
            generation_time = end_time - start_time
            print(f"✅ 启用TurboJPEG生成成功，耗时: {generation_time:.2f}秒")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            
            if images_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                print(f"   - 生成图像文件: {len(image_files)} 个")
                
                if image_files:
                    # 检查第一个文件的大小
                    first_file = image_files[0]
                    file_size = first_file.stat().st_size
                    print(f"   - 第一个文件大小: {file_size} bytes")
                    
                return len(image_files) == 2
            else:
                print("❌ 图像输出目录不存在")
                return False
            
        except Exception as e:
            print(f"❌ 启用TurboJPEG生成失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_turbo_jpeg_disabled_generation():
    """测试禁用TurboJPEG的生成"""
    print("\n=== 测试禁用TurboJPEG的生成 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式便于测试
            'enable_turbo_jpeg': False,  # 禁用TurboJPEG
            'turbo_jpeg_quality': 95,
            'turbo_jpeg_format': 'png'
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 禁用TurboJPEG的MainGenerator创建成功")
            
            # 测试生成时间
            start_time = time.time()
            generator.generate(2)  # 生成2个样本
            end_time = time.time()
            
            generation_time = end_time - start_time
            print(f"✅ 禁用TurboJPEG生成成功，耗时: {generation_time:.2f}秒")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            
            if images_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                print(f"   - 生成图像文件: {len(image_files)} 个")
                
                if image_files:
                    # 检查第一个文件的大小
                    first_file = image_files[0]
                    file_size = first_file.stat().st_size
                    print(f"   - 第一个文件大小: {file_size} bytes")
                    
                return len(image_files) == 2
            else:
                print("❌ 图像输出目录不存在")
                return False
            
        except Exception as e:
            print(f"❌ 禁用TurboJPEG生成失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_mode_turbo_jpeg():
    """测试并行模式下的TurboJPEG配置"""
    print("\n=== 测试并行模式下的TurboJPEG配置 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 90,
            'turbo_jpeg_format': 'auto'
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行模式TurboJPEG的MainGenerator创建成功")
            
            # 测试并行生成
            start_time = time.time()
            generator.generate(3)  # 生成3个样本
            end_time = time.time()
            
            generation_time = end_time - start_time
            print(f"✅ 并行模式TurboJPEG生成成功，耗时: {generation_time:.2f}秒")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return len(image_files) == 3 and len(annotation_files) == 3
            else:
                print("❌ 输出目录不存在")
                return False
            
        except Exception as e:
            print(f"❌ 并行模式TurboJPEG测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数"""
    print("TableRender V5.2 TurboJPEG配置功能测试")
    print("=" * 50)
    
    # 测试配置加载
    config_success = test_turbo_jpeg_config_loading()
    
    # 测试质量参数验证
    quality_success = test_turbo_jpeg_quality_validation()
    
    # 测试格式参数验证
    format_success = test_turbo_jpeg_format_validation()
    
    # 测试启用TurboJPEG的生成
    enabled_success = test_turbo_jpeg_enabled_generation()
    
    # 测试禁用TurboJPEG的生成
    disabled_success = test_turbo_jpeg_disabled_generation()
    
    # 测试并行模式
    parallel_success = test_parallel_mode_turbo_jpeg()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"配置加载: {'✅ 通过' if config_success else '❌ 失败'}")
    print(f"质量参数验证: {'✅ 通过' if quality_success else '❌ 失败'}")
    print(f"格式参数验证: {'✅ 通过' if format_success else '❌ 失败'}")
    print(f"启用TurboJPEG生成: {'✅ 通过' if enabled_success else '❌ 失败'}")
    print(f"禁用TurboJPEG生成: {'✅ 通过' if disabled_success else '❌ 失败'}")
    print(f"并行模式TurboJPEG: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = all([
        config_success,
        quality_success,
        format_success,
        enabled_success,
        disabled_success,
        parallel_success
    ])
    
    if all_success:
        print("\n🎉 所有测试通过！TurboJPEG配置功能正常工作")
        print("现在可以通过配置文件灵活控制TurboJPEG的使用")
        
        print("\n📋 配置示例:")
        print("# 启用TurboJPEG（高速模式）")
        print("performance:")
        print("  enable_turbo_jpeg: true")
        print("  turbo_jpeg_quality: 95")
        print("  turbo_jpeg_format: 'jpeg'")
        print("")
        print("# 禁用TurboJPEG（高质量模式）")
        print("performance:")
        print("  enable_turbo_jpeg: false")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
