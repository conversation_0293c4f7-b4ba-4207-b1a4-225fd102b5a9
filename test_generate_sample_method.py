#!/usr/bin/env python3
"""
测试MainGenerator的generate_sample方法是否存在
"""

import sys
sys.path.insert(0, '.')

def test_generate_sample_method():
    """测试generate_sample方法"""
    print("=== 测试MainGenerator的generate_sample方法 ===")
    
    try:
        # 清理模块缓存
        modules_to_clear = [
            'table_render.main_generator',
            'table_render.config',
            'table_render.utils.parallel_generator'
        ]
        
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
                print(f"清理模块缓存: {module}")
        
        print("1. 导入配置...")
        from table_render.config import RenderConfig
        print("   ✅ 配置导入成功")
        
        print("2. 导入MainGenerator...")
        from table_render.main_generator import MainGenerator
        print("   ✅ MainGenerator导入成功")
        
        print("3. 检查MainGenerator的方法...")
        methods = [method for method in dir(MainGenerator) if not method.startswith('_')]
        print(f"   可用方法: {methods}")
        
        print("4. 检查generate_sample方法...")
        if hasattr(MainGenerator, 'generate_sample'):
            print("   ✅ generate_sample方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(MainGenerator.generate_sample)
            print(f"   方法签名: {sig}")
            
        else:
            print("   ❌ generate_sample方法不存在")
            return False
        
        print("5. 创建MainGenerator实例...")
        config_data = {
            'output': {'output_dir': './test_output'},
            'structure': {'header_rows': 1, 'body_rows': 2, 'cols': 3},
            'content': {'data_source': {'type': 'programmatic'}},
            'style': {'common': {'font': {'font_dirs': ['./fonts/']}}},
        }
        
        config = RenderConfig(**config_data)
        generator = MainGenerator(config, debug_mode=False)
        print("   ✅ MainGenerator实例创建成功")
        
        print("6. 检查实例方法...")
        if hasattr(generator, 'generate_sample'):
            print("   ✅ 实例具有generate_sample方法")
        else:
            print("   ❌ 实例没有generate_sample方法")
            return False
        
        print("7. 检查方法是否可调用...")
        if callable(getattr(generator, 'generate_sample', None)):
            print("   ✅ generate_sample方法可调用")
        else:
            print("   ❌ generate_sample方法不可调用")
            return False
        
        print("\n🎉 所有测试通过！generate_sample方法正常存在")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_generate_sample_method()
    sys.exit(0 if success else 1)
