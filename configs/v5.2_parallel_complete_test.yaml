# TableRender V5.2 完整并行功能测试配置
# 用于验证所有V5.2性能优化功能的集成效果

# ==================== 输出配置 ====================
output:
  output_dir: "./output/"

# ==================== V5.2 性能配置 ====================
performance:
  enable_parallel: true           # 启用样本级并行处理
  max_workers: 4                 # 使用4个工作线程
  max_browser_instances: 4       # 使用4个浏览器实例

# ==================== 调试配置 ====================
debug:
  debug_output_dir: "./debug_output"

# ==================== 表格结构配置 ====================
structure:
  # 使用概率化配置测试并行处理的一致性
  header_rows:
    range_list: [[1, 1], [2, 2]]
    probability_list: [0.7, 0.3]
  
  body_rows:
    range_list: [[3, 5], [6, 8]]
    probability_list: [0.6, 0.4]
  
  cols:
    range_list: [[3, 4], [5, 6]]
    probability_list: [0.5, 0.5]
  
  merge_probability: 0.2
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 12]
      number_probability: 0.4
      empty_cell_probability: 0.1

# ==================== 样式配置 ====================
style:
  common:
    font:
      default_family: ["Arial", "Microsoft YaHei"]
      default_size:
        range_list: [[12, 14], [16, 18]]
        probability_list: [0.7, 0.3]
      bold_probability: 0.15
      italic_probability: 0.1
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: [3, 8]
    randomize_color_probability: 0.4

  inheritance:
    header_inherit_from_body: 0.3
    body_inherit_from_header: 0.2

  border_mode:
    mode: "probabilistic"
    probabilistic:
      mode_list: ["full", "semi", "none"]
      probability_list: [0.5, 0.3, 0.2]

  zebra_stripes: 0.4

  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# ==================== 图像后处理配置 ====================
postprocessing:
  # 透视变换测试
  perspective:
    probability: 0.5
    range_list: [[0.01, 0.03], [0.04, 0.06]]
    probability_list: [0.7, 0.3]
    content_area_shrink_ratio: 0.1

  # 背景图合成测试
  background:
    probability: 0.3
    background_dirs: ["./sample_data"]
    background_dir_probabilities: [1.0]
    render_mode: "css"
    margin_control:
      enable_margin_control: true
      margin_probability: 0.8
      margin_range: [20, 100]

  # 表格透明度测试
  table_blending:
    enable_transparency: true
    default_color_transparency: 0.1
    meaningful_color_transparency: 0.7

  # 降质效果测试
  degradation_blur:
    probability: 0.2
  
  degradation_noise:
    probability: 0.2

# ==================== 随机种子 ====================
seed: 12345
