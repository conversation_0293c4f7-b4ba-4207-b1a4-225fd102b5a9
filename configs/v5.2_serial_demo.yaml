# TableRender V5.2 串行模式演示配置
# 用于性能对比的串行模式配置

# ==================== 输出配置 ====================
output:
  output_dir: "./output/v5.2_serial_demo/"

# ==================== V5.2 性能配置（串行模式）====================
performance:
  enable_parallel: false          # 禁用并行处理（串行模式）
  max_workers: 1                 # 单线程
  max_browser_instances: 1       # 单浏览器实例

# ==================== 表格结构配置 ====================
structure:
  header_rows: 1
  body_rows: 4
  cols: 5
  merge_probability: 0.2
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 12]
      number_probability: 0.4
      empty_cell_probability: 0.1

# ==================== 样式配置 ====================
style:
  common:
    font:
      font_dirs: ["./fonts/"]
      font_dir_probabilities: [1.0]
      size: [10, 16]
    background_color: "#FFFFFF"
    text_color: "#000000"
    
  border_mode:
    mode: "full"
    
  zebra_stripes: 0.4

# ==================== 图像后处理配置 ====================
postprocessing:
  # 背景图合成
  apply_background: true
  background:
    background_dirs: ["./assets/backgrounds/"]
    background_dir_probabilities: [1.0]
    margin_control:
      range_list: [[0.05, 0.15]]
      
  # 透视变换
  apply_perspective: true
  perspective_offset_ratio: [0.02, 0.08]
  
  # 轻微降质效果
  apply_degradation_blur: true
  degradation_blur_kernel_size: [1, 3]
  degradation_blur_sigma: [0.5, 1.5]
  
  apply_degradation_noise: true
  degradation_noise_intensity: [0.01, 0.03]

# ==================== 种子配置 ====================
seed: 42

# ==================== 使用说明 ====================
#
# 此配置用于性能对比测试，展示V5.2并行优化的效果。
#
# 1. 串行模式生成：
#    python -m table_render configs/v5.2_serial_demo.yaml --num-samples 8
#
# 2. 对比并行模式：
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 8
#
# 3. 性能基准测试：
#    python test_v5.2_performance.py
#
# ==================== 预期性能差异 ====================
#
# 📊 8个样本生成时间对比：
# - 串行模式：约8-10分钟
# - 并行模式（4线程）：约2-3分钟
# - 加速比：3-4倍
#
# 💾 资源使用对比：
# - 串行模式：低CPU使用，单浏览器实例
# - 并行模式：高CPU使用，多浏览器实例
# - 内存使用：并行模式约为串行模式的2-4倍
#
# 🎯 适用场景：
# - 串行模式：调试、小批量生成、资源受限环境
# - 并行模式：生产环境、大批量生成、性能优先
