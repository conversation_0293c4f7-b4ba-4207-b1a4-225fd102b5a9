# TableRender V5.2 串行测试配置
# 用于对比性能的串行模式配置

# ==================== 输出配置 ====================
output:
  output_dir: "./output/v5.2_serial_test/"

# ==================== V5.2 性能配置 ====================
performance:
  enable_parallel: false          # 禁用并行处理（串行模式）
  max_workers: 1                 # 单线程
  max_browser_instances: 1       # 单浏览器实例

# ==================== 表格结构配置 ====================
structure:
  header_rows: 1
  body_rows: 3
  cols: 4
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 8]
      number_probability: 0.3
      empty_cell_probability: 0.05

# ==================== 样式配置 ====================
style:
  common:
    font:
      font_dirs: ["./fonts/"]
      font_dir_probabilities: [1.0]
      size: [12, 16]
    background_color: "#FFFFFF"
    text_color: "#000000"
    
  border_mode:
    mode: "full"
    
  zebra_stripes: 0.3

# ==================== 种子配置 ====================
seed: 12345
