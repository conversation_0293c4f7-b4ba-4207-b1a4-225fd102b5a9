# TableRender V5.2 并行测试配置
# 用于验证并行功能的基本配置

# ==================== 输出配置 ====================
output:
  output_dir: "./output/v5.2_parallel_test/"

# ==================== V5.2 性能配置 ====================
performance:
  enable_parallel: true           # 启用样本级并行处理
  max_workers: 2                 # 使用2个工作线程（测试用）
  max_browser_instances: 2       # 使用2个浏览器实例

# ==================== 表格结构配置 ====================
structure:
  header_rows: 1
  body_rows: 3
  cols: 4
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 8]
      number_probability: 0.3
      empty_cell_probability: 0.05

# ==================== 样式配置 ====================
style:
  common:
    font:
      font_dirs: ["./fonts/"]
      font_dir_probabilities: [1.0]
      size: [12, 16]
    background_color: "#FFFFFF"
    text_color: "#000000"
    
  border_mode:
    mode: "full"
    
  zebra_stripes: 0.3

# ==================== 种子配置 ====================
seed: 12345
