# TableRender V5.2 并行处理演示配置
# 专门用于展示V5.2并行功能的简化配置

# ==================== 输出配置 ====================
output:
  output_dir: "./output/v5.2_parallel_demo/"

# ==================== V5.2 性能配置 ====================
performance:
  enable_parallel: true           # 启用样本级并行处理
  max_workers: 4                 # 使用4个工作线程
  max_browser_instances: 4       # 使用4个浏览器实例

# ==================== 表格结构配置 ====================
structure:
  header_rows: 1
  body_rows: 4
  cols: 5
  merge_probability: 0.2
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 12]
      number_probability: 0.4
      empty_cell_probability: 0.1

# ==================== 样式配置 ====================
style:
  common:
    font:
      font_dirs: ["./fonts/"]
      font_dir_probabilities: [1.0]
      size: [10, 16]
    background_color: "#FFFFFF"
    text_color: "#000000"
    
  border_mode:
    mode: "full"
    
  zebra_stripes: 0.4

# ==================== 图像后处理配置 ====================
postprocessing:
  # 背景图合成
  apply_background: true
  background:
    background_dirs: ["./assets/backgrounds/"]
    background_dir_probabilities: [1.0]
    margin_control:
      range_list: [[0.05, 0.15]]
      
  # 透视变换
  apply_perspective: true
  perspective_offset_ratio: [0.02, 0.08]
  
  # 轻微降质效果
  apply_degradation_blur: true
  degradation_blur_kernel_size: [1, 3]
  degradation_blur_sigma: [0.5, 1.5]
  
  apply_degradation_noise: true
  degradation_noise_intensity: [0.01, 0.03]

# ==================== 种子配置 ====================
seed: 42

# ==================== 使用说明 ====================
#
# 1. 基本并行生成（推荐）：
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 8
#
# 2. 调试模式：
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 4 --debug
#
# 3. 大批量生成（展示性能优势）：
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 20
#
# 4. 性能对比测试：
#    # 先禁用并行（修改 enable_parallel: false）
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 8
#    # 再启用并行（修改 enable_parallel: true）
#    python -m table_render configs/v5.2_parallel_demo.yaml --num-samples 8
#
# ==================== V5.2 性能优势 ====================
#
# 🚀 预期性能提升：
# - 4线程并行：3-4倍整体加速
# - TurboJPEG优化：3-5倍图像保存加速
# - 综合效果：8个样本从8分钟降至2-3分钟
#
# 📊 配置说明：
# - enable_parallel: 控制是否启用并行模式
# - max_workers: 工作线程数（建议不超过CPU核心数）
# - max_browser_instances: 浏览器实例数（通常等于线程数）
#
# 💡 优化建议：
# - 小批量测试：2-4个线程
# - 大批量生产：根据系统资源调整
# - 内存不足时：减少max_workers数量
