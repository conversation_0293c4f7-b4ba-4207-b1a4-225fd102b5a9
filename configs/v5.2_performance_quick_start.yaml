# TableRender V5.2 性能优化快速启动配置
# 专为验证V5.2并行性能优化效果而设计的简化配置
# 
# 使用方法：
# python -m table_render configs/v5.2_performance_quick_start.yaml --num-samples 10
# 
# 预期效果：
# - 启用4线程并行处理，预期3-4倍加速
# - TurboJPEG高速图像保存，预期3-5倍保存速度提升
# - 异步I/O优化，减少文件保存等待时间
# - 浏览器实例复用，减少启动开销

# ==================== 输出配置 ====================
output:
  output_dir: "./output/"

# ==================== V5.2 性能优化配置 ====================
performance:
  enable_parallel: true           # 启用样本级并行处理
  max_workers: 4                 # 使用4个工作线程（适合大多数系统）
  max_browser_instances: 4       # 使用4个浏览器实例

# ==================== 简化表格结构配置 ====================
structure:
  header_rows: 1                 # 单行表头
  body_rows: 5                   # 5行表体
  cols: 4                        # 4列
  merge_probability: 0.1         # 10%合并概率
  max_row_span: 2
  max_col_span: 2

# ==================== 简化内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 10]
      number_probability: 0.3
      empty_cell_probability: 0.05

# ==================== 简化样式配置 ====================
style:
  common:
    font:
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 5
    randomize_color_probability: 0.3

  inheritance:
    header_inherit_from_body: 0.3
    body_inherit_from_header: 0.2

  border_mode:
    mode: "full"

  zebra_stripes: 0.3

  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# ==================== 可选的图像后处理配置 ====================
# 取消注释以启用后处理效果（会增加处理时间，但并行优化仍然有效）
#postprocessing:
#  perspective:
#    probability: 0.3
#    range_list: [[0.01, 0.03]]
#    probability_list: [1.0]
#    content_area_shrink_ratio: 0.1
#
#  background:
#    probability: 0.2
#    background_dirs: ["./sample_data"]
#    background_dir_probabilities: [1.0]
#    render_mode: "css"

# ==================== 随机种子 ====================
seed: 42

# ==================== 性能测试建议 ====================
# 
# 1. 基准测试：
#    - 首先设置 enable_parallel: false 测试串行性能
#    - 然后设置 enable_parallel: true 测试并行性能
#    - 对比两次运行的总时间，计算加速比
# 
# 2. 线程数调优：
#    - 从 max_workers: 2 开始测试
#    - 逐步增加到 4, 6, 8，观察性能变化
#    - 找到最佳的线程数配置
# 
# 3. 系统资源监控：
#    - 观察CPU使用率（应接近100%）
#    - 观察内存使用情况
#    - 确保系统不会过载
# 
# 4. 样本数量测试：
#    - 测试不同样本数量：2, 4, 8, 16, 32
#    - 观察并行效率随样本数量的变化
#    - 样本数量越多，并行效果越明显
