# TableRender V5.2 性能配置测试文件
# 用于验证性能优化功能的集成

# ==================== 输出配置 ====================
output:
  output_dir: "./output/"

# ==================== V5.2 性能配置 ====================
performance:
  enable_parallel: true           # 启用样本级并行处理
  max_workers: "auto"            # 自动检测工作线程数
  max_browser_instances: 8       # 最大浏览器实例数

# ==================== 表格结构配置 ====================
structure:
  header_rows: 1
  body_rows: 3
  cols: 4
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

# ==================== 内容配置 ====================
content:
  data_source:
    type: "programmatic"
    programmatic:
      text_length: [3, 8]
      number_probability: 0.3
      empty_cell_probability: 0.05

# ==================== 样式配置 ====================
style:
  common:
    font:
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 5
    randomize_color_probability: 0.3

  inheritance:
    header_inherit_from_body: 0.3
    body_inherit_from_header: 0.2

  border_mode:
    mode: "full"

  zebra_stripes: 0.3

  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# ==================== 随机种子 ====================
seed: 42
