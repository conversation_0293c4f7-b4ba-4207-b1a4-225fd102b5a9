#!/usr/bin/env python3
"""
测试ResolvedParams属性访问修复

验证_analyze_table_size方法的参数传递是否正确。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_analyze_table_size_fix():
    """测试_analyze_table_size方法的修复"""
    print("=== 测试_analyze_table_size方法修复 ===")
    
    # 创建简单的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 3,
            'cols': 4,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': False,  # 先测试串行模式
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ MainGenerator创建成功")
            
            # 测试单个样本生成（这会调用_analyze_table_size）
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 表格大小信息: {result['table_size_info']}")
            
            return True
            
        except AttributeError as e:
            if "'ResolvedParams' object has no attribute 'header_rows'" in str(e):
                print(f"❌ 仍然存在ResolvedParams属性访问错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_mode_fix():
    """测试并行模式下的修复"""
    print("\n=== 测试并行模式下的修复 ===")
    
    # 创建并行测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行MainGenerator创建成功")
            
            # 测试并行生成（这会在多线程中调用_analyze_table_size）
            print("开始测试并行生成...")
            generator.generate(3)  # 生成3个样本
            
            print("✅ 并行生成成功")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return len(image_files) == 3 and len(annotation_files) == 3
            else:
                print("❌ 输出目录不存在")
                return False
            
        except AttributeError as e:
            if "'ResolvedParams' object has no attribute 'header_rows'" in str(e):
                print(f"❌ 并行模式仍然存在ResolvedParams属性访问错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 并行测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数"""
    print("TableRender V5.2 ResolvedParams修复测试")
    print("=" * 50)
    
    # 测试单个样本生成
    single_success = test_analyze_table_size_fix()
    
    # 测试并行模式
    parallel_success = test_parallel_mode_fix()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"单个样本生成: {'✅ 通过' if single_success else '❌ 失败'}")
    print(f"并行模式生成: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = single_success and parallel_success
    
    if all_success:
        print("\n🎉 所有测试通过！ResolvedParams属性访问问题已修复")
        print("现在可以正常使用 TableRender V5.2 的并行功能")
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
