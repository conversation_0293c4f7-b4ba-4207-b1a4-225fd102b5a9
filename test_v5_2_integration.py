#!/usr/bin/env python3
"""
TableRender V5.2 集成测试

验证V5.2的并行生成和TurboJPEG优化功能。
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.generators.main_generator import MainGenerator
from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
from table_render.utils.parallel_generator import ParallelGenerator, ParallelGeneratorConfig


def test_turbo_jpeg_availability():
    """测试TurboJPEG可用性"""
    print("=== 测试TurboJPEG可用性 ===")
    
    saver = TurboJPEGSaver()
    print(f"TurboJPEG可用: {saver.turbo_available}")
    print(f"保存器统计: {saver.get_stats()}")
    
    return saver.turbo_available


def test_performance_config_loading():
    """测试性能配置加载"""
    print("\n=== 测试性能配置加载 ===")
    
    # 测试配置数据
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 3,
            'cols': 4,
            'merge_probability': 0.1,
            'max_row_span': 2,
            'max_col_span': 2
        },
        'performance': {
            'enable_parallel': True,
            'max_workers': 4,
            'max_browser_instances': 4
        }
    }
    
    try:
        config = RenderConfig(**config_data)
        print("✅ 性能配置加载成功")
        
        if config.performance:
            print(f"   - enable_parallel: {config.performance.enable_parallel}")
            print(f"   - max_workers: {config.performance.max_workers}")
            print(f"   - max_browser_instances: {config.performance.max_browser_instances}")
            print(f"   - 解析后的实际工作线程数: {config.performance.resolve_max_workers()}")
            return True
        else:
            print("❌ 性能配置为空")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_serial_generation():
    """测试串行生成模式"""
    print("\n=== 测试串行生成模式 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': False,  # 禁用并行
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            start_time = time.time()
            generator.generate(2)  # 生成2个样本
            serial_time = time.time() - start_time
            
            print(f"✅ 串行生成完成，耗时: {serial_time:.2f}秒")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return serial_time, len(image_files) == 2 and len(annotation_files) == 2
            else:
                print("❌ 输出目录不存在")
                return serial_time, False
                
        except Exception as e:
            print(f"❌ 串行生成失败: {e}")
            return 0, False


def test_parallel_generation():
    """测试并行生成模式"""
    print("\n=== 测试并行生成模式 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': True,  # 启用并行
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            start_time = time.time()
            generator.generate(4)  # 生成4个样本
            parallel_time = time.time() - start_time
            
            print(f"✅ 并行生成完成，耗时: {parallel_time:.2f}秒")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return parallel_time, len(image_files) == 4 and len(annotation_files) == 4
            else:
                print("❌ 输出目录不存在")
                return parallel_time, False
                
        except Exception as e:
            print(f"❌ 并行生成失败: {e}")
            return 0, False


def test_parallel_generator_directly():
    """直接测试并行生成器"""
    print("\n=== 直接测试并行生成器 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'seed': 42
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 创建输出目录结构
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            # 创建并行生成器配置
            parallel_config = ParallelGeneratorConfig(
                max_workers=2,
                enable_turbo_jpeg=True,
                turbo_jpeg_format="jpeg",
                turbo_jpeg_quality=95
            )
            
            # 创建并行生成器
            parallel_generator = ParallelGenerator(config_data, parallel_config)
            
            # 执行并行生成
            start_time = time.time()
            result = parallel_generator.generate_samples(3, output_dirs)
            direct_parallel_time = time.time() - start_time
            
            print(f"✅ 直接并行生成完成，耗时: {direct_parallel_time:.2f}秒")
            print(f"   - 成功样本: {result['stats']['completed_samples']}")
            print(f"   - 失败样本: {result['stats']['failed_samples']}")
            print(f"   - 成功率: {result['success_rate']:.2%}")
            
            # 获取性能摘要
            perf_summary = parallel_generator.get_performance_summary()
            if 'error' not in perf_summary:
                print(f"   - 样本/秒: {perf_summary['samples_per_second']:.2f}")
                print(f"   - 使用线程数: {perf_summary['thread_count']}")
            
            return direct_parallel_time, result['success_rate'] > 0.5
            
        except Exception as e:
            print(f"❌ 直接并行生成失败: {e}")
            return 0, False


def main():
    """主函数"""
    print("TableRender V5.2 集成测试")
    print("=" * 50)
    
    # 测试结果收集
    test_results = {}
    
    # 1. 测试TurboJPEG可用性
    turbo_available = test_turbo_jpeg_availability()
    test_results['turbo_jpeg'] = turbo_available
    
    # 2. 测试性能配置加载
    config_loaded = test_performance_config_loading()
    test_results['config_loading'] = config_loaded
    
    if not config_loaded:
        print("\n❌ 性能配置加载失败，跳过后续测试")
        return False
    
    # 3. 测试串行生成
    serial_time, serial_success = test_serial_generation()
    test_results['serial_generation'] = serial_success
    
    # 4. 测试并行生成
    parallel_time, parallel_success = test_parallel_generation()
    test_results['parallel_generation'] = parallel_success
    
    # 5. 直接测试并行生成器
    direct_parallel_time, direct_success = test_parallel_generator_directly()
    test_results['direct_parallel'] = direct_success
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} | {status}")
    
    # 性能对比
    if serial_time > 0 and parallel_time > 0:
        speedup = serial_time / parallel_time * 2  # 调整样本数差异
        print(f"\n🚀 性能对比:")
        print(f"   串行模式(2样本): {serial_time:.2f}秒")
        print(f"   并行模式(4样本): {parallel_time:.2f}秒")
        print(f"   预估加速比: {speedup:.2f}x")
    
    if direct_parallel_time > 0:
        print(f"   直接并行(3样本): {direct_parallel_time:.2f}秒")
    
    # 总体评估
    all_passed = all(test_results.values())
    
    if all_passed:
        print(f"\n🎉 所有测试通过！V5.2功能正常")
        if turbo_available:
            print("💡 TurboJPEG可用，图像保存将获得3-5倍加速")
        else:
            print("⚠️  TurboJPEG不可用，将使用PIL备选方案")
        return True
    else:
        failed_tests = [name for name, result in test_results.items() if not result]
        print(f"\n💥 部分测试失败: {', '.join(failed_tests)}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
