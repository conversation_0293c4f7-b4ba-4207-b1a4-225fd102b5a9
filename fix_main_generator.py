#!/usr/bin/env python3
"""
修复MainGenerator文件
"""

import os
import shutil

def fix_main_generator():
    """修复MainGenerator文件"""
    print("修复MainGenerator文件...")
    
    # 备份当前文件
    backup_file = "table_render/main_generator.py.backup"
    if os.path.exists("table_render/main_generator.py"):
        shutil.copy("table_render/main_generator.py", backup_file)
        print(f"已备份到: {backup_file}")
    
    # 读取当前文件内容
    with open("table_render/main_generator.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到MainGenerator类的结束位置
    lines = content.split('\n')
    
    # 找到MainGenerator类的结束和LargeTablePerformanceMonitor类的开始
    main_generator_end = -1
    performance_monitor_start = -1
    
    for i, line in enumerate(lines):
        if line.strip() == "class LargeTablePerformanceMonitor:":
            performance_monitor_start = i
            break
    
    if performance_monitor_start == -1:
        print("❌ 找不到LargeTablePerformanceMonitor类")
        return False
    
    # 找到MainGenerator类的实际结束位置（在LargeTablePerformanceMonitor之前）
    for i in range(performance_monitor_start - 1, -1, -1):
        line = lines[i].strip()
        if line and not line.startswith(' ') and not line.startswith('\t'):
            # 这是一个顶级语句，可能是类的结束
            if line.startswith('return ') or line.startswith('pass') or line == '}':
                main_generator_end = i
                break
        elif line.startswith('    ') and (line.strip().startswith('return ') or 'return summary' in line):
            # 这是MainGenerator类中的return语句
            main_generator_end = i
            break
    
    if main_generator_end == -1:
        print("❌ 找不到MainGenerator类的结束位置")
        return False
    
    print(f"MainGenerator类结束于行 {main_generator_end + 1}")
    print(f"LargeTablePerformanceMonitor类开始于行 {performance_monitor_start + 1}")
    
    # 构建新的文件内容
    new_lines = []
    
    # 添加MainGenerator类的内容（到结束位置）
    new_lines.extend(lines[:main_generator_end + 1])
    
    # 添加generate_sample方法
    generate_sample_method = '''
    def generate_sample(self, sample_index: int, use_turbo_jpeg: bool = False) -> Dict[str, Any]:
        """
        生成单个样本（用于并行生成）
        
        Args:
            sample_index: 样本索引
            use_turbo_jpeg: 是否使用TurboJPEG优化
            
        Returns:
            样本生成结果
        """
        # 使用asyncio运行异步生成过程
        return asyncio.run(self._async_generate_sample(sample_index, use_turbo_jpeg))
    
    async def _async_generate_sample(self, sample_index: int, use_turbo_jpeg: bool = False) -> Dict[str, Any]:
        """
        异步生成单个样本
        
        Args:
            sample_index: 样本索引
            use_turbo_jpeg: 是否使用TurboJPEG优化
            
        Returns:
            样本生成结果
        """
        self.logger.debug(f"开始生成样本 {sample_index}")
        
        # 确保输出目录存在
        output_dirs = FileUtils.ensure_output_dirs(self.config.output.output_dir)
        
        # 初始化渲染器和转换器
        renderer = await HtmlRenderer.create_async()
        annotation_converter = AnnotationConverter()
        
        try:
            # 设置当前样本索引用于调试
            self._current_sample_index = sample_index
            
            # V5.1新增：开始性能监控
            sample_start_time = time.time()
            initial_memory = self.performance_monitor.get_memory_usage()
            
            # 为每个样本生成一个独立的随机种子
            with profile_stage("seed_generation"):
                sample_seed = self.random_state.randint(0, 2**32 - 1)
            
            # V3.1新工作流：首先解析配置为具体参数
            with profile_stage("config_resolution", {"sample_seed": sample_seed}):
                resolved_params = self.resolver.resolve(self.config, sample_seed)
            
            # V5.1新增：检查是否为大表格并进行预检查
            with profile_stage("table_size_analysis"):
                table_size_info = self._analyze_table_size(resolved_params.structure)
                if table_size_info['is_large_table']:
                    self.logger.info(f"[LARGE_TABLE] 检测到大表格: {table_size_info['description']}")
                    
                    # 内存预检查
                    if not self.performance_monitor.check_memory_availability(self.memory_threshold_mb):
                        self.logger.warning(f"[LARGE_TABLE] 内存不足，跳过样本 {sample_index}")
                        raise RuntimeError(f"内存不足，无法生成样本 {sample_index}")
            
            # 构建表格结构
            with profile_stage("structure_building", {
                "rows": resolved_params.structure.body_rows + resolved_params.structure.header_rows,
                "cols": resolved_params.structure.cols
            }):
                structure_builder = StructureBuilder(sample_seed)
                table_model = structure_builder.build(
                    resolved_params.structure,
                    resolved_params.style.border_mode,
                    resolved_params.style.border_details
                )
            
            # 填充表格内容
            with profile_stage("content_building", {
                "source_type": resolved_params.content.source_type
            }):
                content_builder = ContentBuilder(sample_seed)
                table_model = content_builder.build(table_model, resolved_params.content)
            
            # 生成样式
            with profile_stage("style_building"):
                style_builder = StyleBuilder(sample_seed)
                # V4.3新增：传递透明度配置
                transparency_config = resolved_params.postprocessing if resolved_params.postprocessing else None
                css_string = style_builder.build(resolved_params.style, table_model, transparency_config)
            
            # 设置当前样本的调试目录
            if self.debug_mode:
                sample_debug_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{sample_index:06d}")
                renderer.debug_mode = True
                renderer.debug_output_dir = sample_debug_dir
                renderer.debug_stage_counter = 0  # 重置计数器
                # 确保调试目录存在
                Path(sample_debug_dir).mkdir(parents=True, exist_ok=True)
            else:
                renderer.debug_mode = False
                renderer.debug_output_dir = None
            
            # 简化的渲染流程（避免复杂的后处理逻辑）
            with profile_stage("html_rendering"):
                image_bytes, raw_annotations = await renderer.render(
                    table_model, css_string, resolved_params.postprocessing, self.config
                )
            
            # 转换标注格式
            with profile_stage("annotation_conversion"):
                image_filename = f"{sample_index:06d}.png"
                final_annotations = annotation_converter.convert_to_final_format(
                    raw_annotations, table_model, image_filename
                )
            
            # 应用图像后处理（如果配置了）
            if resolved_params.postprocessing:
                debug_output_dir = None
                if self.debug_mode and self.config.debug:
                    base_debug_dir = self.config.debug.debug_output_dir
                    debug_output_dir = os.path.join(base_debug_dir, f"debug_sample_{sample_index:06d}")
                
                with profile_stage("image_augmentor_init"):
                    image_augmentor = ImageAugmentor(
                        sample_seed,
                        debug_mode=self.debug_mode and self.config.debug is not None,
                        debug_output_dir=debug_output_dir
                    )
                
                image_bytes, final_annotations = image_augmentor.process(
                    image_bytes, final_annotations, resolved_params.postprocessing
                )
            
            # 准备元数据
            with profile_stage("json_serialization"):
                metadata = {
                    "sample_index": sample_index,
                    "sample_seed": sample_seed,
                    "generation_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "config_hash": str(hash(str(self.config))),
                    "table_size": table_size_info
                }
                
                # 转换为可序列化格式
                serializable_annotations = annotation_converter.to_serializable(final_annotations)
            
            # 保存文件
            with profile_stage("file_saving", {
                "image_size_bytes": len(image_bytes),
                "annotation_count": len(serializable_annotations.get('cells', []))
            }):
                self.logger.debug(f"保存文件前的最终标注: {final_annotations}")
                FileUtils.save_sample(
                    sample_index=sample_index,
                    image_bytes=image_bytes,
                    annotations=serializable_annotations,
                    metadata=metadata,
                    output_dirs=output_dirs,
                    label_suffix=resolved_params.output.label_suffix,
                    use_turbo=use_turbo_jpeg,  # V5.2新增：使用TurboJPEG优化
                    format_hint="jpeg" if use_turbo_jpeg else None  # V5.2新增：格式提示
                )
                self.logger.debug(f"文件保存完成: 样本 {sample_index}")
            
            # V5.1新增：记录性能监控信息
            sample_end_time = time.time()
            final_memory = self.performance_monitor.get_memory_usage()
            sample_duration = sample_end_time - sample_start_time
            memory_delta = final_memory - initial_memory
            
            # 记录性能信息
            perf_info = {
                'sample_index': sample_index,
                'duration_seconds': round(sample_duration, 2),
                'initial_memory_mb': round(initial_memory, 2),
                'final_memory_mb': round(final_memory, 2),
                'memory_delta_mb': round(memory_delta, 2),
                'table_size': table_size_info
            }
            
            self.performance_monitor.record_sample_performance(perf_info)
            
            # 记录日志
            if table_size_info['is_large_table']:
                self.logger.info(f"[LARGE_TABLE_PERF] 样本 {sample_index} 完成")
                self.logger.info(f"[LARGE_TABLE_PERF] 耗时: {sample_duration:.2f}秒")
                self.logger.info(f"[LARGE_TABLE_PERF] 内存变化: {memory_delta:.2f}MB")
            else:
                self.logger.info(f"样本 {sample_index} 生成完成，耗时: {sample_duration:.2f}秒")
            
            # 返回生成结果
            return {
                'sample_index': sample_index,
                'success': True,
                'duration_seconds': sample_duration,
                'memory_delta_mb': memory_delta,
                'image_size_bytes': len(image_bytes),
                'annotation_count': len(serializable_annotations.get('cells', [])),
                'table_size_info': table_size_info,
                'turbo_jpeg_used': use_turbo_jpeg
            }
            
        except Exception as e:
            self.logger.error(f"生成样本 {sample_index} 失败: {e}")
            raise
        
        finally:
            # 清理渲染器资源
            if renderer:
                await renderer.close()
'''
    
    new_lines.extend(generate_sample_method.split('\n'))
    
    # 添加空行
    new_lines.append('')
    new_lines.append('')
    
    # 添加LargeTablePerformanceMonitor类（从原始位置开始）
    # 但是要跳过被破坏的部分，直接找到正确的类定义
    found_correct_class = False
    for i in range(performance_monitor_start, len(lines)):
        line = lines[i]
        if line.strip() == "class LargeTablePerformanceMonitor:" and not found_correct_class:
            found_correct_class = True
            new_lines.append(line)
        elif found_correct_class and line.strip().startswith('"""'):
            # 找到正确的类文档字符串
            new_lines.append('    """')
            new_lines.append('    V5.1新增：大表格性能监控器')
            new_lines.append('')
            new_lines.append('    监控内存使用、渲染时间等性能指标，特别针对大表格优化')
            new_lines.append('    """')
            break
    
    # 添加LargeTablePerformanceMonitor的方法
    performance_methods = '''
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.performance_records = []
        self.process = psutil.Process()

    def get_memory_usage(self):
        """
        获取当前内存使用量（MB）
        """
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / 1024 / 1024  # 转换为MB
        except Exception as e:
            self.logger.warning(f"获取内存使用量失败: {e}")
            return 0

    def check_memory_availability(self, threshold_mb):
        """
        检查内存可用性
        
        Args:
            threshold_mb: 内存阈值（MB）
            
        Returns:
            bool: 内存是否充足
        """
        try:
            import psutil
            
            # 获取当前进程内存使用
            current_memory = self.get_memory_usage()
            
            # 获取系统可用内存
            available_memory = psutil.virtual_memory().available / 1024 / 1024  # MB
            
            # 检查当前内存使用是否超过阈值
            if current_memory > threshold_mb:
                self.logger.warning(f"当前内存使用 {current_memory:.2f}MB 超过阈值 {threshold_mb}MB")
                return False

            if available_memory < threshold_mb:
                self.logger.warning(f"可用内存 {available_memory:.2f}MB 低于阈值 {threshold_mb}MB")
                return False

            return True
        except Exception as e:
            self.logger.warning(f"内存检查失败: {e}")
            return True  # 检查失败时允许继续

    def record_sample_performance(self, perf_info):
        """
        记录样本性能信息
        
        Args:
            perf_info: 性能信息字典
        """
        self.performance_records.append(perf_info)

    def get_performance_summary(self):
        """
        获取性能总结
        
        Returns:
            dict: 性能总结信息
        """
        if not self.performance_records:
            return {}

        # 分析大表格和普通表格的性能
        large_table_records = [r for r in self.performance_records if r.get('table_size', {}).get('is_large_table', False)]
        normal_table_records = [r for r in self.performance_records if not r.get('table_size', {}).get('is_large_table', False)]

        summary = {
            'total_samples': len(self.performance_records),
            'large_table_count': len(large_table_records),
            'normal_table_count': len(normal_table_records)
        }

        if large_table_records:
            large_durations = [r['duration_seconds'] for r in large_table_records]
            large_memory_deltas = [r['memory_delta_mb'] for r in large_table_records]

            summary['large_table_performance'] = {
                'avg_duration': sum(large_durations) / len(large_durations),
                'max_duration': max(large_durations),
                'avg_memory_delta': sum(large_memory_deltas) / len(large_memory_deltas),
                'max_memory_delta': max(large_memory_deltas)
            }

        if normal_table_records:
            normal_durations = [r['duration_seconds'] for r in normal_table_records]
            normal_memory_deltas = [r['memory_delta_mb'] for r in normal_table_records]

            summary['normal_table_performance'] = {
                'avg_duration': sum(normal_durations) / len(normal_durations),
                'max_duration': max(normal_durations),
                'avg_memory_delta': sum(normal_memory_deltas) / len(normal_memory_deltas),
                'max_memory_delta': max(normal_memory_deltas)
            }

        return summary
'''
    
    new_lines.extend(performance_methods.split('\n'))
    
    # 写入新文件
    new_content = '\n'.join(new_lines)
    
    with open("table_render/main_generator.py", 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ MainGenerator文件修复完成")
    return True

if __name__ == "__main__":
    success = fix_main_generator()
    if success:
        print("🎉 修复成功！")
    else:
        print("❌ 修复失败！")
