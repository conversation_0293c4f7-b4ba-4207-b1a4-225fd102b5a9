#!/usr/bin/env python3
"""
完整的V5.2并行功能测试
"""

import sys
import os
import time
import yaml
sys.path.insert(0, '.')

def test_complete_parallel_functionality():
    """测试完整的并行功能"""
    print("=== V5.2并行功能完整测试 ===")
    
    try:
        # 清理模块缓存
        modules_to_clear = [
            'table_render.main_generator',
            'table_render.config',
            'table_render.utils.parallel_generator',
            'table_render.utils.turbo_jpeg_saver'
        ]
        
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        print("1. 测试所有导入...")
        from table_render.config import RenderConfig
        from table_render.main_generator import MainGenerator
        from table_render.utils.parallel_generator import ParallelGenerator
        from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
        print("   ✅ 所有导入成功")
        
        print("2. 测试配置加载...")
        config_data = {
            'output': {'output_dir': './test_output/v5.2_test'},
            'structure': {'header_rows': 1, 'body_rows': 2, 'cols': 3},
            'content': {'data_source': {'type': 'programmatic'}},
            'style': {'common': {'font': {'font_dirs': ['./fonts/']}}},
            'performance': {
                'enable_parallel': True,
                'max_workers': 2,
                'max_browser_instances': 2
            }
        }
        
        config = RenderConfig(**config_data)
        print(f"   ✅ 配置加载成功: parallel={config.performance.enable_parallel}")
        
        print("3. 测试MainGenerator创建...")
        generator = MainGenerator(config, debug_mode=False)
        print("   ✅ MainGenerator创建成功")
        
        print("4. 测试generate_sample方法...")
        if hasattr(generator, 'generate_sample'):
            print("   ✅ generate_sample方法存在")
        else:
            print("   ❌ generate_sample方法不存在")
            return False
        
        print("5. 测试ParallelGenerator创建...")
        parallel_gen = ParallelGenerator(config, max_workers=2)
        print("   ✅ ParallelGenerator创建成功")
        
        print("6. 测试TurboJPEG保存器...")
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        stats = saver.get_stats()
        print(f"   ✅ TurboJPEG状态: available={stats['turbo_available']}")
        
        print("7. 测试并行生成（小规模）...")
        start_time = time.time()
        
        # 创建一个简单的进度回调
        def progress_callback(completed, total):
            print(f"   进度: {completed}/{total} ({completed/total*100:.1f}%)")
        
        parallel_gen.progress_callback = progress_callback
        
        # 生成2个样本
        results = parallel_gen.generate_samples(2, start_index=0)
        end_time = time.time()
        
        print(f"   ✅ 并行生成完成")
        print(f"   总耗时: {end_time - start_time:.2f}秒")
        print(f"   成功样本: {results['successful_samples']}")
        print(f"   失败样本: {results['failed_samples']}")
        
        if results['successful_samples'] > 0:
            print(f"   平均耗时: {results['average_time_per_sample']:.2f}秒/样本")
        
        if results['failed_samples'] > 0:
            print(f"   失败索引: {results['failed_indices']}")
            return False
        
        print("8. 验证输出文件...")
        output_dir = config.output.output_dir
        if os.path.exists(output_dir):
            images_dir = os.path.join(output_dir, 'images')
            annotations_dir = os.path.join(output_dir, 'annotations')
            
            if os.path.exists(images_dir):
                image_files = [f for f in os.listdir(images_dir) if f.endswith(('.png', '.jpg', '.jpeg'))]
                print(f"   生成图像文件: {len(image_files)}个")
            
            if os.path.exists(annotations_dir):
                annotation_files = [f for f in os.listdir(annotations_dir) if f.endswith('.json')]
                print(f"   生成标注文件: {len(annotation_files)}个")
        
        print("\n🎉 V5.2并行功能完整测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_generator_direct():
    """直接测试MainGenerator的并行模式"""
    print("\n=== 直接测试MainGenerator并行模式 ===")
    
    try:
        from table_render.config import RenderConfig
        from table_render.main_generator import MainGenerator
        
        # 创建并行配置
        config_data = {
            'output': {'output_dir': './test_output/v5.2_direct_test'},
            'structure': {'header_rows': 1, 'body_rows': 2, 'cols': 3},
            'content': {'data_source': {'type': 'programmatic'}},
            'style': {'common': {'font': {'font_dirs': ['./fonts/']}}},
            'performance': {
                'enable_parallel': True,
                'max_workers': 2,
                'max_browser_instances': 2
            }
        }
        
        config = RenderConfig(**config_data)
        generator = MainGenerator(config, debug_mode=False)
        
        print("开始生成2个样本（并行模式）...")
        start_time = time.time()
        
        # 这应该自动使用并行模式
        generator.generate(2)
        
        end_time = time.time()
        print(f"✅ 生成完成，耗时: {end_time - start_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("TableRender V5.2 并行功能完整测试")
    print("=" * 50)
    
    # 测试1: 完整的并行功能
    test1_success = test_complete_parallel_functionality()
    
    # 测试2: 直接测试MainGenerator
    test2_success = test_main_generator_direct()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"完整并行功能测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"MainGenerator直接测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！V5.2并行功能正常工作")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
