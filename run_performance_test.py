#!/usr/bin/env python3
"""
TableRender 性能测试脚本

用于运行性能分析并生成详细的性能报告
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from table_render.main import main as table_render_main


def setup_performance_logging():
    """设置性能测试专用的日志配置"""
    # 创建性能测试输出目录
    perf_output_dir = Path("./performance_test_output")
    perf_output_dir.mkdir(exist_ok=True)
    
    # 设置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(perf_output_dir / "performance_test.log", mode='w'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return perf_output_dir


def create_test_config(output_dir: Path, num_samples: int = 20):
    """创建性能测试配置文件"""
    config_content = f"""# TableRender 性能测试配置
# 专门用于性能分析的配置，包含各种表格大小

output:
  output_dir: "{output_dir / 'generated_samples'}"
  label_suffix: "_perf_test"

structure:
  # 测试不同大小的表格
  body_rows:
    type: "range"
    min: 3
    max: 15  # 中等大小表格，避免过大影响测试速度
  cols:
    type: "range" 
    min: 3
    max: 8   # 中等列数
  header_rows: 1
  merge_probability: 0.1  # 适度的合并概率

content:
  source_type: "programmatic"
  programmatic_types: ["text", "currency", "percentage"]

style:
  common:
    font:
      font_dirs: ["./fonts"]
      default_family: "Arial"
      default_size: 12
    padding: 8
  inheritance:
    header_inherit_probability: 0.7
    body_inherit_probability: 0.5
  border_mode:
    mode: "semi"
    row_line_probability: 0.6
    col_line_probability: 0.6
    outer_frame: true
    header_separator: true

# 启用后处理以测试完整流程
postprocessing:
  apply_background: true
  background_image_dir: "./backgrounds"
  apply_perspective: true
  perspective_offset_ratio: 0.1
  apply_degradation_blur: true
  degradation_blur_kernel_size: 3
  degradation_blur_sigma: 1.0

# 启用调试模式以获取详细性能数据
debug:
  debug_output_dir: "{output_dir / 'debug'}"

seed: 42
"""
    
    config_path = output_dir / "performance_test_config.yaml"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    return config_path


def run_performance_test(config_path: Path, num_samples: int):
    """运行性能测试"""
    print(f"🚀 开始性能测试...")
    print(f"📊 配置文件: {config_path}")
    print(f"🔢 样本数量: {num_samples}")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    start_time = time.time()
    
    try:
        # 构造命令行参数
        sys.argv = [
            'table_render',
            str(config_path),
            '--num-samples', str(num_samples),
            '--debug'  # 启用调试模式以获取性能数据
        ]
        
        # 运行TableRender
        table_render_main()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print("="*60)
        print(f"✅ 性能测试完成!")
        print(f"⏱️  总耗时: {total_duration:.2f}秒")
        print(f"📈 平均每样本: {total_duration/num_samples:.2f}秒")
        print(f"🏁 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_performance_results(output_dir: Path):
    """分析性能测试结果"""
    print("\n" + "="*60)
    print("📊 性能分析结果")
    print("="*60)
    
    # 查找性能数据文件
    perf_dir = output_dir / "debug" / "performance"
    if not perf_dir.exists():
        print("❌ 未找到性能数据目录")
        return
    
    perf_json_file = perf_dir / "performance_data.json"
    perf_log_file = perf_dir / "performance_detailed.log"
    
    if perf_json_file.exists():
        print(f"📄 性能数据文件: {perf_json_file}")
        try:
            import json
            with open(perf_json_file, 'r', encoding='utf-8') as f:
                perf_data = json.load(f)
            
            print(f"📊 总样本数: {perf_data.get('total_samples', 0)}")
            print(f"📈 总指标数: {perf_data.get('total_metrics', 0)}")
            
            # 显示各阶段统计
            stage_stats = perf_data.get('stage_statistics', {})
            if stage_stats:
                print("\n🔍 各阶段性能统计 (按平均耗时排序):")
                sorted_stages = sorted(stage_stats.items(), 
                                     key=lambda x: x[1].get('avg_duration_ms', 0), 
                                     reverse=True)
                
                for stage_name, stats in sorted_stages[:10]:  # 显示前10个最耗时的阶段
                    avg_ms = stats.get('avg_duration_ms', 0)
                    count = stats.get('count', 0)
                    max_ms = stats.get('max_duration', 0) * 1000
                    print(f"  {stage_name:30} | 平均: {avg_ms:8.2f}ms | 调用: {count:3d}次 | 最大: {max_ms:8.2f}ms")
            
        except Exception as e:
            print(f"❌ 解析性能数据失败: {e}")
    
    if perf_log_file.exists():
        print(f"\n📄 详细性能日志: {perf_log_file}")
        print("💡 可以查看该文件获取更详细的性能分析信息")
    
    print(f"\n📁 完整结果目录: {output_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TableRender 性能测试工具')
    parser.add_argument('--samples', type=int, default=20, 
                       help='生成的样本数量 (默认: 20)')
    parser.add_argument('--output-dir', type=str, default='./performance_test_output',
                       help='输出目录 (默认: ./performance_test_output)')
    
    args = parser.parse_args()
    
    # 设置输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 设置日志
    setup_performance_logging()
    
    print("🔬 TableRender 性能测试工具")
    print("="*60)
    
    try:
        # 创建测试配置
        config_path = create_test_config(output_dir, args.samples)
        print(f"📝 已创建测试配置: {config_path}")
        
        # 运行性能测试
        success = run_performance_test(config_path, args.samples)
        
        if success:
            # 分析结果
            analyze_performance_results(output_dir)
            
            print(f"\n🎉 性能测试完成! 结果保存在: {output_dir}")
            print("\n📋 下一步:")
            print("1. 查看性能数据文件了解详细指标")
            print("2. 分析性能日志找出瓶颈")
            print("3. 根据结果优化代码")
            
        else:
            print("\n❌ 性能测试失败，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
