#!/usr/bin/env python3
"""
测试所有V5.2相关的导入是否正确
"""

import sys
import traceback

def test_import(module_name, description):
    """测试单个模块导入"""
    try:
        if module_name == "table_render.config":
            from table_render.config import RenderConfig, PerformanceConfig
            print(f"✅ {description}: 成功导入 RenderConfig, PerformanceConfig")
        elif module_name == "table_render.utils.turbo_jpeg_saver":
            from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
            print(f"✅ {description}: 成功导入 TurboJPEGSaver")
        elif module_name == "table_render.utils.parallel_generator":
            from table_render.utils.parallel_generator import ParallelGenerator, ParallelGeneratorConfig
            print(f"✅ {description}: 成功导入 ParallelGenerator, ParallelGeneratorConfig")
        elif module_name == "table_render.utils.file_utils":
            from table_render.utils.file_utils import FileUtils
            print(f"✅ {description}: 成功导入 FileUtils")
        elif module_name == "table_render.main_generator":
            from table_render.main_generator import MainGenerator
            print(f"✅ {description}: 成功导入 MainGenerator")
        elif module_name == "table_render.utils":
            from table_render.utils import TurboJPEGSaver
            # ParallelGenerator需要单独导入以避免循环导入
            from table_render.utils.parallel_generator import ParallelGenerator, ParallelGeneratorConfig
            print(f"✅ {description}: 成功导入 utils 模块的类")
        else:
            exec(f"import {module_name}")
            print(f"✅ {description}: 成功导入")
        return True
    except Exception as e:
        print(f"❌ {description}: 导入失败")
        print(f"   错误: {e}")
        print(f"   详细信息: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("TableRender V5.2 导入测试")
    print("=" * 50)
    
    tests = [
        ("table_render.config", "配置模块"),
        ("table_render.utils.turbo_jpeg_saver", "TurboJPEG保存器"),
        ("table_render.utils.parallel_generator", "并行生成器"),
        ("table_render.utils.file_utils", "文件工具"),
        ("table_render.main_generator", "主生成器"),
        ("table_render.utils", "工具模块统一导入"),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for module_name, description in tests:
        if test_import(module_name, description):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有导入测试通过！V5.2模块可以正常使用")
        
        # 额外测试：创建实例
        print("\n额外测试：创建实例...")
        try:
            from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
            from table_render.utils.parallel_generator import ParallelGeneratorConfig
            from table_render.config import PerformanceConfig
            
            # 测试TurboJPEGSaver
            saver = TurboJPEGSaver()
            print(f"✅ TurboJPEGSaver实例创建成功，TurboJPEG可用: {saver.turbo_available}")
            
            # 测试ParallelGeneratorConfig
            parallel_config = ParallelGeneratorConfig(max_workers=2)
            print(f"✅ ParallelGeneratorConfig实例创建成功，工作线程数: {parallel_config.max_workers}")
            
            # 测试PerformanceConfig
            perf_config = PerformanceConfig(enable_parallel=True, max_workers="auto")
            resolved_workers = perf_config.resolve_max_workers()
            print(f"✅ PerformanceConfig实例创建成功，解析后工作线程数: {resolved_workers}")
            
            print("🎉 所有实例创建测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 实例创建测试失败: {e}")
            print(f"   详细信息: {traceback.format_exc()}")
            return False
    else:
        print("💥 部分导入测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
