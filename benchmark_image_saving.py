#!/usr/bin/env python3
"""
图像保存性能基准测试

对比PIL和TurboJPEG的保存性能差异。
"""

import os
import time
import tempfile
import statistics
from pathlib import Path
from PIL import Image
import numpy as np
import io

# 添加项目路径
import sys
sys.path.insert(0, '.')

from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver


def create_test_images(count: int = 5, width: int = 1200, height: int = 800):
    """创建多个测试图像"""
    images = []
    for i in range(count):
        # 创建随机图像
        image_array = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
        
        # 添加一些结构化内容
        for row in range(0, height, 60):
            image_array[row:row+2, :] = [0, 0, 0]
        for col in range(0, width, 80):
            image_array[:, col:col+2] = [0, 0, 0]
        
        # 添加彩色块
        for r in range(height // 60):
            for c in range(width // 80):
                y_start = r * 60 + 5
                y_end = y_start + 50
                x_start = c * 80 + 5
                x_end = x_start + 70
                
                if y_end < height and x_end < width:
                    color = [
                        np.random.randint(50, 256),
                        np.random.randint(50, 256),
                        np.random.randint(50, 256)
                    ]
                    image_array[y_start:y_end, x_start:x_end] = color
        
        images.append(Image.fromarray(image_array))
    
    return images


def benchmark_pil_saving(images, temp_dir, format_type='PNG'):
    """基准测试PIL保存"""
    times = []
    
    for i, image in enumerate(images):
        file_path = temp_dir / f"pil_{i}.{format_type.lower()}"
        
        start_time = time.time()
        if format_type.upper() == 'JPEG':
            # 确保RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            image.save(file_path, format='JPEG', quality=95, optimize=True)
        else:
            image.save(file_path, format='PNG', optimize=True)
        end_time = time.time()
        
        times.append(end_time - start_time)
    
    return times


def benchmark_turbojpeg_saving(images, temp_dir, format_type='PNG'):
    """基准测试TurboJPEG保存"""
    saver = TurboJPEGSaver(quality=95, enable_turbo=True)
    times = []
    
    for i, image in enumerate(images):
        file_path = temp_dir / f"turbo_{i}.{format_type.lower()}"
        
        start_time = time.time()
        success = saver.save_image(image, file_path, format_hint=format_type.lower())
        end_time = time.time()
        
        if success:
            times.append(end_time - start_time)
        else:
            print(f"TurboJPEG保存失败: {file_path}")
            times.append(float('inf'))
    
    return times


def benchmark_bytes_saving(images, temp_dir):
    """基准测试字节数据保存（模拟pil_to_image_bytes瓶颈）"""
    # 先将图像转换为字节数据（模拟当前的pil_to_image_bytes阶段）
    image_bytes_list = []
    pil_to_bytes_times = []
    
    print("转换PIL图像为字节数据...")
    for image in images:
        start_time = time.time()
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()
        end_time = time.time()
        
        image_bytes_list.append(image_bytes)
        pil_to_bytes_times.append(end_time - start_time)
    
    # 测试原始字节保存
    original_times = []
    for i, image_bytes in enumerate(image_bytes_list):
        file_path = temp_dir / f"original_bytes_{i}.png"
        
        start_time = time.time()
        with open(file_path, 'wb') as f:
            f.write(image_bytes)
        end_time = time.time()
        
        original_times.append(end_time - start_time)
    
    # 测试TurboJPEG字节保存
    saver = TurboJPEGSaver(quality=95, enable_turbo=True)
    turbo_times = []
    
    for i, image_bytes in enumerate(image_bytes_list):
        file_path = temp_dir / f"turbo_bytes_{i}.jpg"
        
        start_time = time.time()
        success = saver.save_image_bytes(image_bytes, file_path, format_hint="jpeg")
        end_time = time.time()
        
        if success:
            turbo_times.append(end_time - start_time)
        else:
            turbo_times.append(float('inf'))
    
    return pil_to_bytes_times, original_times, turbo_times


def print_statistics(name, times):
    """打印统计信息"""
    if not times or all(t == float('inf') for t in times):
        print(f"{name}: 无有效数据")
        return
    
    valid_times = [t for t in times if t != float('inf')]
    if not valid_times:
        print(f"{name}: 无有效数据")
        return
    
    avg_time = statistics.mean(valid_times)
    min_time = min(valid_times)
    max_time = max(valid_times)
    
    print(f"{name}:")
    print(f"  平均时间: {avg_time*1000:.2f}ms")
    print(f"  最小时间: {min_time*1000:.2f}ms")
    print(f"  最大时间: {max_time*1000:.2f}ms")
    print(f"  总时间: {sum(valid_times)*1000:.2f}ms")
    
    return avg_time


def main():
    """主函数"""
    print("=== 图像保存性能基准测试 ===")
    
    # 创建测试图像
    print("创建测试图像...")
    test_images = create_test_images(count=10, width=1200, height=800)
    print(f"创建了 {len(test_images)} 个测试图像 (1200x800)")
    
    # 检查TurboJPEG可用性
    saver = TurboJPEGSaver()
    print(f"TurboJPEG可用: {saver.turbo_available}")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        print("\n--- PNG格式保存测试 ---")
        
        # PIL PNG保存
        pil_png_times = benchmark_pil_saving(test_images, temp_path, 'PNG')
        pil_png_avg = print_statistics("PIL PNG保存", pil_png_times)
        
        # TurboJPEG PNG保存（实际上还是用PIL）
        turbo_png_times = benchmark_turbojpeg_saving(test_images, temp_path, 'PNG')
        turbo_png_avg = print_statistics("TurboJPEG PNG保存", turbo_png_times)
        
        print("\n--- JPEG格式保存测试 ---")
        
        # PIL JPEG保存
        pil_jpeg_times = benchmark_pil_saving(test_images, temp_path, 'JPEG')
        pil_jpeg_avg = print_statistics("PIL JPEG保存", pil_jpeg_times)
        
        # TurboJPEG JPEG保存
        turbo_jpeg_times = benchmark_turbojpeg_saving(test_images, temp_path, 'JPEG')
        turbo_jpeg_avg = print_statistics("TurboJPEG JPEG保存", turbo_jpeg_times)
        
        print("\n--- 字节数据保存测试（模拟pil_to_image_bytes瓶颈）---")
        
        pil_to_bytes_times, original_times, turbo_times = benchmark_bytes_saving(test_images, temp_path)
        
        pil_to_bytes_avg = print_statistics("PIL转字节数据", pil_to_bytes_times)
        original_avg = print_statistics("原始字节保存", original_times)
        turbo_avg = print_statistics("TurboJPEG字节保存", turbo_times)
        
        print("\n--- 性能对比总结 ---")
        
        if saver.turbo_available:
            if pil_jpeg_avg and turbo_jpeg_avg and turbo_jpeg_avg > 0:
                jpeg_speedup = pil_jpeg_avg / turbo_jpeg_avg
                print(f"JPEG保存加速比: {jpeg_speedup:.2f}x")
            
            if original_avg and turbo_avg and turbo_avg > 0:
                bytes_speedup = original_avg / turbo_avg
                print(f"字节保存加速比: {bytes_speedup:.2f}x")
            
            # 模拟完整的pil_to_image_bytes优化效果
            if pil_to_bytes_avg and turbo_avg and turbo_avg > 0:
                total_original = pil_to_bytes_avg + original_avg if original_avg else pil_to_bytes_avg
                total_speedup = total_original / turbo_avg
                print(f"完整流程加速比: {total_speedup:.2f}x")
                print(f"预期节省时间: {(total_original - turbo_avg)*1000:.2f}ms per image")
        else:
            print("TurboJPEG不可用，无法计算加速比")
        
        print("\n--- 文件大小对比 ---")
        
        # 检查文件大小
        png_files = list(temp_path.glob("*.png"))
        jpg_files = list(temp_path.glob("*.jpg"))
        
        if png_files:
            avg_png_size = statistics.mean([f.stat().st_size for f in png_files])
            print(f"PNG平均文件大小: {avg_png_size/1024:.2f} KB")
        
        if jpg_files:
            avg_jpg_size = statistics.mean([f.stat().st_size for f in jpg_files])
            print(f"JPEG平均文件大小: {avg_jpg_size/1024:.2f} KB")
            
            if png_files:
                compression_ratio = avg_png_size / avg_jpg_size
                print(f"JPEG压缩比: {compression_ratio:.2f}x")
    
    print("\n=== 基准测试完成 ===")


if __name__ == "__main__":
    main()
