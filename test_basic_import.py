#!/usr/bin/env python3
"""
测试基本导入
"""

import sys
sys.path.insert(0, '.')

print("测试基本导入...")

try:
    print("1. 导入config...")
    from table_render.config import RenderConfig
    print("   ✅ config导入成功")
    
    print("2. 导入turbo_jpeg_saver...")
    from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
    print("   ✅ turbo_jpeg_saver导入成功")
    
    print("3. 导入parallel_generator...")
    from table_render.utils.parallel_generator import ParallelGenerator
    print("   ✅ parallel_generator导入成功")
    
    print("4. 导入main_generator...")
    from table_render.main_generator import MainGenerator
    print("   ✅ main_generator导入成功")
    
    print("5. 导入table_render模块...")
    import table_render
    print("   ✅ table_render模块导入成功")
    
    print("\n🎉 所有基本导入测试通过！")
    
except Exception as e:
    print(f"\n❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
