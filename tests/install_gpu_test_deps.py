#!/usr/bin/env python3
"""
GPU测试依赖安装脚本

自动安装GPU加速测试所需的依赖包
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("🔧 安装GPU测试依赖包...")
    
    packages = [
        ("numpy", "numpy"),
        ("opencv-python", "cv2"),
        ("pynvml", "pynvml"),
    ]
    
    # 可选包（安装失败不影响基本测试）
    optional_packages = [
        ("torch", "torch"),
        ("opencv-contrib-python", None),  # 升级opencv
    ]
    
    success_count = 0
    total_count = len(packages)
    
    # 安装必需包
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    # 安装可选包
    print("\n📦 安装可选包（失败不影响基本测试）...")
    for package_name, import_name in optional_packages:
        install_package(package_name, import_name)
    
    print(f"\n📊 安装结果: {success_count}/{total_count} 必需包安装成功")
    
    if success_count == total_count:
        print("✅ 所有必需依赖安装完成，可以运行GPU测试了！")
        print("运行命令: python tests/test_gpu_acceleration.py")
        return 0
    else:
        print("❌ 部分依赖安装失败，可能影响测试结果")
        return 1

if __name__ == "__main__":
    sys.exit(main())
