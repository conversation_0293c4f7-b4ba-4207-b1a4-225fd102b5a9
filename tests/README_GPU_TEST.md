# GPU加速能力测试指南

## 📋 测试目的

这个测试脚本用于检测你的服务器是否支持GPU加速的图像处理，为TableRender V5.2的性能优化提供技术可行性评估。

## 🚀 快速开始

### 1. 安装测试依赖

```bash
# 方法1: 使用自动安装脚本（推荐）
python tests/install_gpu_test_deps.py

# 方法2: 手动安装
pip install numpy opencv-python pynvml torch
```

### 2. 运行GPU测试

```bash
python tests/test_gpu_acceleration.py
```

## 📊 测试结果解读

### ✅ 成功情况

如果看到以下输出，说明你的服务器支持GPU加速：

```
🎉 恭喜！你的服务器支持GPU加速，可以进行TableRender V5.2优化！
```

**关键指标**：
- `CUDA可用: ✅` - NVIDIA GPU和驱动正常
- `OpenCV GPU可用: ✅` - 可以使用OpenCV GPU加速
- `CuPy可用: ✅` - 可以使用CuPy GPU计算
- `GPU加速比: X.XXx` - GPU比CPU快的倍数

### ❌ 失败情况

如果看到以下输出，说明暂时不支持GPU加速：

```
😞 抱歉，你的服务器暂时不支持GPU加速，建议使用CPU优化方案。
```

**常见问题和解决方案**：

#### 问题1: 未检测到CUDA环境
```
❌ 未检测到CUDA环境，无法使用GPU加速
```
**解决方案**：
1. 检查是否有NVIDIA GPU：`nvidia-smi`
2. 安装NVIDIA驱动
3. 安装CUDA Toolkit
4. 重启服务器

#### 问题2: OpenCV不支持GPU
```
❌ OpenCV未检测到CUDA设备
```
**解决方案**：
1. 安装GPU版本的OpenCV：`pip install opencv-contrib-python`
2. 或者编译支持CUDA的OpenCV

#### 问题3: CuPy未安装
```
❌ CuPy未安装
```
**解决方案**：
```bash
# 根据你的CUDA版本选择（查看：nvidia-smi）
pip install cupy-cuda11x  # CUDA 11.x
pip install cupy-cuda12x  # CUDA 12.x
```

## 📈 性能基准测试

测试脚本会进行以下性能测试：

1. **CPU基准测试**
   - 高斯模糊处理
   - 形态学操作
   - 颜色空间转换

2. **GPU性能测试**
   - OpenCV GPU加速测试
   - CuPy数组计算测试
   - 数据传输性能测试

3. **加速比计算**
   - GPU vs CPU性能对比
   - 内存使用分析

## 🎯 判断标准

### 强烈推荐使用GPU加速
- GPU加速比 > 2.0x
- GPU可用内存 > 2GB
- 所有GPU测试通过

### 可以考虑GPU加速
- GPU加速比 > 1.2x
- GPU可用内存 > 1GB
- 部分GPU测试通过

### 建议使用CPU优化
- GPU加速比 < 1.2x
- GPU可用内存 < 1GB
- GPU测试大部分失败

## 📄 测试结果文件

测试完成后会生成 `tests/gpu_test_results.json` 文件，包含详细的测试数据：

```json
{
  "cuda_available": true,
  "opencv_gpu_available": true,
  "cupy_available": true,
  "gpu_devices": [...],
  "performance_tests": {...},
  "recommendations": [...]
}
```

## 🔧 故障排除

### 常见错误1: 导入错误
```
ImportError: No module named 'cv2'
```
**解决**：`pip install opencv-python`

### 常见错误2: CUDA版本不匹配
```
RuntimeError: CUDA version mismatch
```
**解决**：确保PyTorch/CuPy的CUDA版本与系统CUDA版本匹配

### 常见错误3: GPU内存不足
```
RuntimeError: CUDA out of memory
```
**解决**：
1. 关闭其他GPU程序
2. 减少测试图像大小
3. 使用更大内存的GPU

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. 测试脚本的完整输出
2. `nvidia-smi` 命令输出
3. Python版本和操作系统信息
4. `gpu_test_results.json` 文件内容

## 🔄 下一步

### 如果GPU测试通过
恭喜！你可以继续进行TableRender V5.2的GPU加速优化：
1. 开始实施GPU加速的图像处理
2. 集成OpenCV CUDA模块
3. 优化内存使用策略

### 如果GPU测试失败
不用担心！你仍然可以进行CPU优化：
1. 使用多线程图像处理
2. 优化PIL图像序列化
3. 实施内存管理优化
4. 考虑使用更快的CPU图像处理库

---

**注意**：这个测试脚本专门为TableRender V5.2性能优化设计，测试的是实际图像处理场景的GPU加速能力。
