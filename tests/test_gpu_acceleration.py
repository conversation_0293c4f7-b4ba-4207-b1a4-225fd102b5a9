#!/usr/bin/env python3
"""
GPU加速能力测试脚本

用于检测服务器是否支持GPU加速的图像处理，包括：
1. CUDA环境检测
2. OpenCV GPU支持检测
3. 图像处理性能基准测试
4. 内存使用情况分析

使用方法:
    python tests/test_gpu_acceleration.py
"""

import sys
import time
import traceback
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class GPUAccelerationTester:
    """GPU加速能力测试器"""
    
    def __init__(self):
        self.results = {
            'cuda_available': False,
            'opencv_gpu_available': False,
            'cupy_available': False,
            'gpu_devices': [],
            'performance_tests': {},
            'recommendations': []
        }
        
    def test_cuda_environment(self):
        """测试CUDA环境"""
        print("🔍 检测CUDA环境...")
        
        try:
            import pynvml
            pynvml.nvmlInit()
            device_count = pynvml.nvmlDeviceGetCount()
            
            print(f"✅ 检测到 {device_count} 个NVIDIA GPU设备:")
            
            for i in range(device_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                gpu_info = {
                    'index': i,
                    'name': name,
                    'memory_total': memory_info.total // 1024**2,  # MB
                    'memory_free': memory_info.free // 1024**2,   # MB
                    'memory_used': memory_info.used // 1024**2    # MB
                }
                
                self.results['gpu_devices'].append(gpu_info)
                print(f"  GPU {i}: {name}")
                print(f"    总内存: {gpu_info['memory_total']}MB")
                print(f"    可用内存: {gpu_info['memory_free']}MB")
                print(f"    已用内存: {gpu_info['memory_used']}MB")
            
            self.results['cuda_available'] = True
            
        except ImportError:
            print("❌ pynvml未安装，无法检测NVIDIA GPU")
            print("   安装命令: pip install pynvml")
        except Exception as e:
            print(f"❌ CUDA环境检测失败: {e}")
            
        # 尝试检测CUDA toolkit
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✅ PyTorch检测到CUDA: {torch.version.cuda}")
                print(f"   可用GPU数量: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    props = torch.cuda.get_device_properties(i)
                    print(f"   GPU {i}: {props.name} (计算能力: {props.major}.{props.minor})")
            else:
                print("❌ PyTorch未检测到可用的CUDA设备")
        except ImportError:
            print("⚠️  PyTorch未安装，无法进行CUDA检测")
    
    def test_opencv_gpu(self):
        """测试OpenCV GPU支持"""
        print("\n🔍 检测OpenCV GPU支持...")
        
        try:
            import cv2
            print(f"OpenCV版本: {cv2.__version__}")
            
            # 检查CUDA支持
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                print(f"✅ OpenCV检测到 {cv2.cuda.getCudaEnabledDeviceCount()} 个CUDA设备")
                self.results['opencv_gpu_available'] = True
                
                # 获取设备信息
                for i in range(cv2.cuda.getCudaEnabledDeviceCount()):
                    print(f"   CUDA设备 {i}: 可用")
                    
            else:
                print("❌ OpenCV未检测到CUDA设备")
                print("   可能原因:")
                print("   1. OpenCV编译时未启用CUDA支持")
                print("   2. CUDA驱动未正确安装")
                print("   3. GPU不支持CUDA")
                
        except ImportError:
            print("❌ OpenCV未安装")
        except Exception as e:
            print(f"❌ OpenCV GPU检测失败: {e}")
    
    def test_cupy_support(self):
        """测试CuPy支持"""
        print("\n🔍 检测CuPy支持...")
        
        try:
            import cupy as cp
            print(f"✅ CuPy版本: {cp.__version__}")
            
            # 测试基本GPU操作
            try:
                x = cp.array([1, 2, 3, 4, 5])
                y = cp.sum(x)
                print(f"✅ CuPy基本操作测试通过: sum([1,2,3,4,5]) = {y}")
                self.results['cupy_available'] = True
            except Exception as e:
                print(f"❌ CuPy基本操作测试失败: {e}")
                
        except ImportError:
            print("❌ CuPy未安装")
            print("   安装命令: pip install cupy-cuda11x  # 根据CUDA版本选择")
        except Exception as e:
            print(f"❌ CuPy测试失败: {e}")
    
    def performance_benchmark(self):
        """性能基准测试"""
        print("\n🚀 开始性能基准测试...")
        
        # 创建测试图像（模拟TableRender的大图像）
        test_image_size = (4320, 7680, 3)  # 7680x4320 RGB图像
        print(f"测试图像尺寸: {test_image_size[1]}x{test_image_size[0]} ({test_image_size[2]}通道)")
        
        # CPU基准测试
        self._test_cpu_performance(test_image_size)
        
        # GPU基准测试（如果可用）
        if self.results['opencv_gpu_available']:
            self._test_opencv_gpu_performance(test_image_size)
        
        if self.results['cupy_available']:
            self._test_cupy_performance(test_image_size)
    
    def _test_cpu_performance(self, image_size):
        """CPU性能测试"""
        print("\n📊 CPU性能测试...")
        
        try:
            import cv2
            
            # 创建测试图像
            print("  创建测试图像...")
            img = np.random.randint(0, 255, image_size, dtype=np.uint8)
            
            # 测试高斯模糊（TableRender中的常用操作）
            print("  测试高斯模糊...")
            start_time = time.time()
            blurred = cv2.GaussianBlur(img, (15, 15), 0)
            cpu_blur_time = time.time() - start_time
            
            # 测试形态学操作
            print("  测试形态学操作...")
            kernel = np.ones((5, 5), np.uint8)
            start_time = time.time()
            morphed = cv2.morphologyEx(img, cv2.MORPH_CLOSE, kernel)
            cpu_morph_time = time.time() - start_time
            
            # 测试颜色空间转换
            print("  测试颜色空间转换...")
            start_time = time.time()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            cpu_cvt_time = time.time() - start_time
            
            self.results['performance_tests']['cpu'] = {
                'gaussian_blur': cpu_blur_time,
                'morphology': cpu_morph_time,
                'color_convert': cpu_cvt_time,
                'total': cpu_blur_time + cpu_morph_time + cpu_cvt_time
            }
            
            print(f"  ✅ CPU测试完成:")
            print(f"     高斯模糊: {cpu_blur_time:.3f}秒")
            print(f"     形态学操作: {cpu_morph_time:.3f}秒")
            print(f"     颜色转换: {cpu_cvt_time:.3f}秒")
            print(f"     总耗时: {cpu_blur_time + cpu_morph_time + cpu_cvt_time:.3f}秒")
            
        except Exception as e:
            print(f"  ❌ CPU性能测试失败: {e}")
    
    def _test_opencv_gpu_performance(self, image_size):
        """OpenCV GPU性能测试"""
        print("\n📊 OpenCV GPU性能测试...")
        
        try:
            import cv2
            
            # 创建测试图像并上传到GPU
            print("  创建测试图像并上传到GPU...")
            img = np.random.randint(0, 255, image_size, dtype=np.uint8)
            gpu_img = cv2.cuda_GpuMat()
            gpu_img.upload(img)
            
            # 测试GPU高斯模糊
            print("  测试GPU高斯模糊...")
            start_time = time.time()
            gpu_blurred = cv2.cuda.GaussianBlur(gpu_img, (15, 15), 0)
            result = gpu_blurred.download()
            gpu_blur_time = time.time() - start_time
            
            # 测试GPU颜色空间转换
            print("  测试GPU颜色空间转换...")
            start_time = time.time()
            gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
            result = gpu_gray.download()
            gpu_cvt_time = time.time() - start_time
            
            self.results['performance_tests']['opencv_gpu'] = {
                'gaussian_blur': gpu_blur_time,
                'color_convert': gpu_cvt_time,
                'total': gpu_blur_time + gpu_cvt_time
            }
            
            print(f"  ✅ OpenCV GPU测试完成:")
            print(f"     高斯模糊: {gpu_blur_time:.3f}秒")
            print(f"     颜色转换: {gpu_cvt_time:.3f}秒")
            print(f"     总耗时: {gpu_blur_time + gpu_cvt_time:.3f}秒")
            
            # 计算加速比
            if 'cpu' in self.results['performance_tests']:
                cpu_total = self.results['performance_tests']['cpu']['total']
                gpu_total = gpu_blur_time + gpu_cvt_time
                speedup = cpu_total / gpu_total if gpu_total > 0 else 0
                print(f"     🚀 GPU加速比: {speedup:.2f}x")
            
        except Exception as e:
            print(f"  ❌ OpenCV GPU性能测试失败: {e}")
            traceback.print_exc()
    
    def _test_cupy_performance(self, image_size):
        """CuPy性能测试"""
        print("\n📊 CuPy性能测试...")
        
        try:
            import cupy as cp
            
            # 创建测试数据
            print("  创建测试数据...")
            cpu_array = np.random.rand(*image_size).astype(np.float32)
            
            # 测试数据传输
            start_time = time.time()
            gpu_array = cp.asarray(cpu_array)
            upload_time = time.time() - start_time
            
            # 测试GPU计算
            start_time = time.time()
            result = cp.sum(gpu_array, axis=2)  # 模拟图像处理操作
            compute_time = time.time() - start_time
            
            # 测试数据下载
            start_time = time.time()
            result_cpu = cp.asnumpy(result)
            download_time = time.time() - start_time
            
            total_time = upload_time + compute_time + download_time
            
            self.results['performance_tests']['cupy'] = {
                'upload': upload_time,
                'compute': compute_time,
                'download': download_time,
                'total': total_time
            }
            
            print(f"  ✅ CuPy测试完成:")
            print(f"     数据上传: {upload_time:.3f}秒")
            print(f"     GPU计算: {compute_time:.3f}秒")
            print(f"     数据下载: {download_time:.3f}秒")
            print(f"     总耗时: {total_time:.3f}秒")
            
        except Exception as e:
            print(f"  ❌ CuPy性能测试失败: {e}")
    
    def generate_recommendations(self):
        """生成优化建议"""
        print("\n💡 优化建议:")
        
        if not self.results['cuda_available']:
            print("❌ 未检测到CUDA环境，无法使用GPU加速")
            print("   建议:")
            print("   1. 检查是否安装了NVIDIA GPU驱动")
            print("   2. 安装CUDA Toolkit")
            print("   3. 考虑使用CPU优化方案")
            self.results['recommendations'].append("install_cuda")
            return
        
        if not self.results['opencv_gpu_available']:
            print("⚠️  OpenCV不支持GPU，建议重新编译或安装GPU版本")
            print("   安装命令: pip install opencv-contrib-python")
            self.results['recommendations'].append("install_opencv_gpu")
        
        if not self.results['cupy_available']:
            print("⚠️  CuPy未安装，建议安装以获得更好的GPU加速")
            print("   安装命令: pip install cupy-cuda11x  # 根据CUDA版本选择")
            self.results['recommendations'].append("install_cupy")
        
        # 分析性能测试结果
        if 'opencv_gpu' in self.results['performance_tests'] and 'cpu' in self.results['performance_tests']:
            cpu_time = self.results['performance_tests']['cpu']['total']
            gpu_time = self.results['performance_tests']['opencv_gpu']['total']
            speedup = cpu_time / gpu_time if gpu_time > 0 else 0
            
            if speedup > 2:
                print(f"✅ GPU加速效果显著 ({speedup:.2f}x)，强烈建议使用GPU加速")
                self.results['recommendations'].append("use_gpu_acceleration")
            elif speedup > 1.2:
                print(f"✅ GPU加速有一定效果 ({speedup:.2f}x)，建议使用GPU加速")
                self.results['recommendations'].append("consider_gpu_acceleration")
            else:
                print(f"⚠️  GPU加速效果不明显 ({speedup:.2f}x)，可能受到数据传输开销影响")
                self.results['recommendations'].append("optimize_data_transfer")
        
        # GPU内存建议
        if self.results['gpu_devices']:
            max_memory = max(gpu['memory_free'] for gpu in self.results['gpu_devices'])
            if max_memory < 2000:  # 小于2GB
                print(f"⚠️  GPU可用内存较少 ({max_memory}MB)，建议:")
                print("   1. 使用图像分块处理")
                print("   2. 降低处理图像分辨率")
                print("   3. 及时释放GPU内存")
                self.results['recommendations'].append("optimize_gpu_memory")
            else:
                print(f"✅ GPU内存充足 ({max_memory}MB)，可以处理大图像")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 TableRender GPU加速能力测试")
        print("=" * 50)
        
        self.test_cuda_environment()
        self.test_opencv_gpu()
        self.test_cupy_support()
        self.performance_benchmark()
        self.generate_recommendations()
        
        print("\n" + "=" * 50)
        print("📋 测试总结:")
        print(f"CUDA可用: {'✅' if self.results['cuda_available'] else '❌'}")
        print(f"OpenCV GPU可用: {'✅' if self.results['opencv_gpu_available'] else '❌'}")
        print(f"CuPy可用: {'✅' if self.results['cupy_available'] else '❌'}")
        print(f"GPU设备数量: {len(self.results['gpu_devices'])}")
        
        return self.results


def main():
    """主函数"""
    tester = GPUAccelerationTester()
    results = tester.run_all_tests()
    
    # 保存测试结果
    import json
    results_file = Path(__file__).parent / "gpu_test_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细测试结果已保存到: {results_file}")
    
    # 返回是否可以使用GPU加速
    can_use_gpu = (results['cuda_available'] and 
                   (results['opencv_gpu_available'] or results['cupy_available']))
    
    if can_use_gpu:
        print("\n🎉 恭喜！你的服务器支持GPU加速，可以进行TableRender V5.2优化！")
        return 0
    else:
        print("\n😞 抱歉，你的服务器暂时不支持GPU加速，建议使用CPU优化方案。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
