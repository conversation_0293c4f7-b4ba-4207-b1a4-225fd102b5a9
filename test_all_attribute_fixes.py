#!/usr/bin/env python3
"""
测试所有ResolvedParams属性访问修复

验证所有属性访问错误是否已修复。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_serial_mode_with_postprocessing():
    """测试串行模式下的后处理功能"""
    print("=== 测试串行模式下的后处理功能 ===")
    
    # 创建包含后处理的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_perspective': True,
            'perspective_offset_ratio': [0.01, 0.03],
            'apply_background': False,  # 暂时禁用背景以简化测试
            'apply_degradation_blur': True,
            'degradation_blur_kernel_size': [1, 2],
            'degradation_blur_sigma': [0.5, 1.0]
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 串行MainGenerator创建成功")
            
            # 测试单个样本生成
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成（包含后处理）...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 表格大小信息: {result['table_size_info']}")
            
            return True
            
        except AttributeError as e:
            if "object has no attribute" in str(e):
                print(f"❌ 仍然存在属性访问错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_mode_with_postprocessing():
    """测试并行模式下的后处理功能"""
    print("\n=== 测试并行模式下的后处理功能 ===")
    
    # 创建包含后处理的并行测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_perspective': True,
            'perspective_offset_ratio': [0.01, 0.03],
            'apply_background': False,  # 暂时禁用背景以简化测试
            'apply_degradation_noise': True,
            'degradation_noise_intensity': [0.01, 0.02]
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行MainGenerator创建成功")
            
            # 测试并行生成
            print("开始测试并行生成（包含后处理）...")
            generator.generate(3)  # 生成3个样本
            
            print("✅ 并行生成成功")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return len(image_files) == 3 and len(annotation_files) == 3
            else:
                print("❌ 输出目录不存在")
                return False
            
        except AttributeError as e:
            if "object has no attribute" in str(e):
                print(f"❌ 并行模式仍然存在属性访问错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 并行测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_config_validation():
    """测试配置验证"""
    print("\n=== 测试配置验证 ===")
    
    try:
        # 测试ResolvedPostprocessingParams的属性
        from table_render.config import ResolvedPostprocessingParams
        
        # 创建一个ResolvedPostprocessingParams实例
        resolved_postprocessing = ResolvedPostprocessingParams(
            apply_perspective=True,
            perspective_offset_ratio=0.02,
            apply_background=True,
            background_image_path="/path/to/bg.jpg",
            apply_degradation_blur=True,
            apply_degradation_noise=False
        )
        
        print("✅ ResolvedPostprocessingParams实例创建成功")
        print(f"   - apply_perspective: {resolved_postprocessing.apply_perspective}")
        print(f"   - apply_background: {resolved_postprocessing.apply_background}")
        print(f"   - apply_degradation_blur: {resolved_postprocessing.apply_degradation_blur}")
        
        # 验证属性存在
        assert hasattr(resolved_postprocessing, 'apply_perspective')
        assert hasattr(resolved_postprocessing, 'apply_background')
        assert hasattr(resolved_postprocessing, 'apply_degradation_blur')
        assert hasattr(resolved_postprocessing, 'apply_degradation_noise')
        
        # 验证不存在错误的属性
        assert not hasattr(resolved_postprocessing, 'background')
        assert not hasattr(resolved_postprocessing, 'perspective')
        assert not hasattr(resolved_postprocessing, 'degradation_blur')
        
        print("✅ 属性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


def main():
    """主函数"""
    print("TableRender V5.2 属性访问修复测试")
    print("=" * 50)
    
    # 测试配置验证
    config_success = test_config_validation()
    
    # 测试串行模式
    serial_success = test_serial_mode_with_postprocessing()
    
    # 测试并行模式
    parallel_success = test_parallel_mode_with_postprocessing()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"配置验证: {'✅ 通过' if config_success else '❌ 失败'}")
    print(f"串行模式: {'✅ 通过' if serial_success else '❌ 失败'}")
    print(f"并行模式: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = config_success and serial_success and parallel_success
    
    if all_success:
        print("\n🎉 所有测试通过！属性访问问题已全部修复")
        print("现在可以正常使用 TableRender V5.2 的所有功能")
        
        print("\n📋 修复总结:")
        print("1. ✅ resolved_params.postprocessing.background → apply_background")
        print("2. ✅ resolved_params.postprocessing.perspective → apply_perspective")
        print("3. ✅ resolved_params.structure.header_rows 访问修复")
        print("4. ✅ 降质效果属性访问修复")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
