#!/usr/bin/env python3
"""
调试导入错误
"""

import sys
import os
sys.path.insert(0, '.')

def debug_imports():
    """逐步调试导入问题"""
    
    print("=== 调试导入错误 ===")
    
    # 1. 检查Python路径
    print(f"Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 2. 检查table_render目录是否存在
    if os.path.exists('table_render'):
        print("✅ table_render目录存在")
        print(f"   内容: {os.listdir('table_render')}")
    else:
        print("❌ table_render目录不存在")
        return
    
    # 3. 检查config.py文件
    config_path = 'table_render/config.py'
    if os.path.exists(config_path):
        print("✅ config.py文件存在")
        
        # 检查文件内容
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class RenderConfig' in content:
                print("✅ RenderConfig类存在")
            else:
                print("❌ RenderConfig类不存在")
                
            if 'class Config' in content:
                print("⚠️ 发现Config类（可能导致混淆）")
            else:
                print("✅ 没有Config类")
    else:
        print("❌ config.py文件不存在")
        return
    
    # 4. 检查utils目录
    utils_path = 'table_render/utils'
    if os.path.exists(utils_path):
        print("✅ utils目录存在")
        print(f"   内容: {os.listdir(utils_path)}")
        
        # 检查__init__.py
        utils_init = os.path.join(utils_path, '__init__.py')
        if os.path.exists(utils_init):
            print("✅ utils/__init__.py存在")
            with open(utils_init, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   内容预览: {content[:200]}...")
        else:
            print("❌ utils/__init__.py不存在")
            
        # 检查parallel_generator.py
        parallel_gen_path = os.path.join(utils_path, 'parallel_generator.py')
        if os.path.exists(parallel_gen_path):
            print("✅ parallel_generator.py存在")
            with open(parallel_gen_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'from ..config import RenderConfig' in content:
                    print("✅ 正确导入RenderConfig")
                elif 'from ..config import Config' in content:
                    print("❌ 错误导入Config")
                else:
                    print("⚠️ 未找到config导入")
        else:
            print("❌ parallel_generator.py不存在")
    else:
        print("❌ utils目录不存在")
        return
    
    # 5. 尝试逐步导入
    print("\n=== 逐步导入测试 ===")
    
    try:
        print("1. 导入table_render...")
        import table_render
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return
    
    try:
        print("2. 导入table_render.config...")
        import table_render.config
        print("   ✅ 成功")
        print(f"   可用属性: {[attr for attr in dir(table_render.config) if not attr.startswith('_')]}")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return
    
    try:
        print("3. 导入RenderConfig...")
        from table_render.config import RenderConfig
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return
    
    try:
        print("4. 导入table_render.utils...")
        import table_render.utils
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return
    
    try:
        print("5. 导入turbo_jpeg_saver...")
        from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return
    
    try:
        print("6. 导入parallel_generator...")
        from table_render.utils.parallel_generator import ParallelGenerator
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    try:
        print("7. 导入main_generator...")
        from table_render.main_generator import MainGenerator
        print("   ✅ 成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 所有导入测试通过！")

if __name__ == "__main__":
    debug_imports()
