#!/usr/bin/env python3
"""
TableRender V5.2 性能基准测试

对比V5.2并行优化前后的性能差异，验证预期的3-6倍加速效果。
"""

import logging
import time
import yaml
from pathlib import Path
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results = []
    
    def run_benchmark(self, sample_counts: List[int] = None) -> Dict[str, Any]:
        """
        运行性能基准测试
        
        Args:
            sample_counts: 要测试的样本数量列表
            
        Returns:
            基准测试结果
        """
        if sample_counts is None:
            sample_counts = [2, 4, 8, 12]
        
        self.logger.info("=== TableRender V5.2 性能基准测试 ===")
        
        for num_samples in sample_counts:
            self.logger.info(f"\n测试样本数量: {num_samples}")
            
            # 串行模式测试
            serial_time = self._test_serial_mode(num_samples)
            
            # 并行模式测试
            parallel_time = self._test_parallel_mode(num_samples)
            
            # 计算加速比
            speedup = serial_time / parallel_time if parallel_time > 0 else 0
            
            result = {
                'num_samples': num_samples,
                'serial_time': serial_time,
                'parallel_time': parallel_time,
                'speedup': speedup,
                'time_saved': serial_time - parallel_time,
                'efficiency': speedup / 4  # 假设4线程
            }
            
            self.results.append(result)
            
            self.logger.info(f"串行时间: {serial_time:.2f}秒")
            self.logger.info(f"并行时间: {parallel_time:.2f}秒")
            self.logger.info(f"加速比: {speedup:.2f}x")
            self.logger.info(f"节省时间: {result['time_saved']:.2f}秒")
        
        # 生成报告
        self._generate_report()
        
        return {
            'results': self.results,
            'summary': self._calculate_summary()
        }
    
    def _test_serial_mode(self, num_samples: int) -> float:
        """测试串行模式"""
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 4,
                'cols': 5
            },
            'performance': {
                'enable_parallel': False,
                'max_workers': 1,
                'max_browser_instances': 1
            }
        }
        
        config = RenderConfig(**config_data)
        generator = MainGenerator(config, debug_mode=False)
        
        start_time = time.time()
        generator.generate(num_samples)
        return time.time() - start_time
    
    def _test_parallel_mode(self, num_samples: int) -> float:
        """测试并行模式"""
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 4,
                'cols': 5
            },
            'performance': {
                'enable_parallel': True,
                'max_workers': 4,
                'max_browser_instances': 4
            }
        }
        
        config = RenderConfig(**config_data)
        generator = MainGenerator(config, debug_mode=False)
        
        start_time = time.time()
        generator.generate(num_samples)
        return time.time() - start_time
    
    def _calculate_summary(self) -> Dict[str, Any]:
        """计算汇总统计"""
        if not self.results:
            return {}
        
        speedups = [r['speedup'] for r in self.results]
        time_savings = [r['time_saved'] for r in self.results]
        
        return {
            'avg_speedup': sum(speedups) / len(speedups),
            'max_speedup': max(speedups),
            'min_speedup': min(speedups),
            'total_time_saved': sum(time_savings),
            'avg_time_saved': sum(time_savings) / len(time_savings),
            'test_count': len(self.results)
        }
    
    def _generate_report(self):
        """生成性能报告"""
        self.logger.info("\n=== 性能基准测试报告 ===")
        
        if not self.results:
            self.logger.info("没有测试结果")
            return
        
        # 表格标题
        self.logger.info(f"{'样本数':<8} {'串行时间':<10} {'并行时间':<10} {'加速比':<8} {'节省时间':<10} {'效率':<8}")
        self.logger.info("-" * 60)
        
        # 表格数据
        for result in self.results:
            self.logger.info(
                f"{result['num_samples']:<8} "
                f"{result['serial_time']:<10.2f} "
                f"{result['parallel_time']:<10.2f} "
                f"{result['speedup']:<8.2f} "
                f"{result['time_saved']:<10.2f} "
                f"{result['efficiency']:<8.2f}"
            )
        
        # 汇总统计
        summary = self._calculate_summary()
        self.logger.info("\n=== 汇总统计 ===")
        self.logger.info(f"平均加速比: {summary['avg_speedup']:.2f}x")
        self.logger.info(f"最大加速比: {summary['max_speedup']:.2f}x")
        self.logger.info(f"最小加速比: {summary['min_speedup']:.2f}x")
        self.logger.info(f"总节省时间: {summary['total_time_saved']:.2f}秒")
        self.logger.info(f"平均节省时间: {summary['avg_time_saved']:.2f}秒")
        
        # 性能评估
        avg_speedup = summary['avg_speedup']
        if avg_speedup >= 3.0:
            self.logger.info("🎉 性能优化效果优秀！达到预期目标")
        elif avg_speedup >= 2.0:
            self.logger.info("✅ 性能优化效果良好")
        elif avg_speedup >= 1.5:
            self.logger.info("⚠️ 性能优化效果一般")
        else:
            self.logger.info("❌ 性能优化效果不佳")
    
    def save_results_to_file(self, filename: str = "benchmark_results.yaml"):
        """保存结果到文件"""
        results_data = {
            'benchmark_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'results': self.results,
            'summary': self._calculate_summary()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            yaml.dump(results_data, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"基准测试结果已保存到: {filename}")


def main():
    """主函数"""
    # 确保输出目录存在
    Path("./output").mkdir(parents=True, exist_ok=True)
    
    # 运行基准测试
    benchmark = PerformanceBenchmark()
    
    # 测试不同的样本数量
    sample_counts = [2, 4, 6, 8]
    
    try:
        results = benchmark.run_benchmark(sample_counts)
        
        # 保存结果
        benchmark.save_results_to_file("v5.2_benchmark_results.yaml")
        
        # 检查是否达到预期性能
        summary = results['summary']
        if summary['avg_speedup'] >= 2.0:
            logger.info("\n🎉 V5.2性能优化验证成功！")
            exit(0)
        else:
            logger.warning(f"\n⚠️ 性能提升({summary['avg_speedup']:.2f}x)未达到预期目标(2.0x+)")
            exit(1)
            
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
