#!/usr/bin/env python3
"""
TableRender V5.2 简化性能基准测试

对比串行和并行模式的性能差异，验证V5.2优化效果。
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.generators.main_generator import MainGenerator


def create_test_config(enable_parallel: bool = False, max_workers: int = 2):
    """创建测试配置"""
    return {
        'structure': {
            'header_rows': 1,
            'body_rows': 4,
            'cols': 5,
            'merge_probability': 0.1,
            'max_row_span': 2,
            'max_col_span': 2
        },
        'content': {
            'source_type': 'programmatic'
        },
        'style': {
            'common': {
                'font': {
                    'default_family': 'Arial',
                    'default_size': 12
                },
                'randomize_color_probability': 0.2
            },
            'border_mode': {
                'mode': 'full'
            }
        },
        'performance': {
            'enable_parallel': enable_parallel,
            'max_workers': max_workers,
            'max_browser_instances': max_workers
        },
        'seed': 42
    }


def run_benchmark_test(mode: str, num_samples: int, enable_parallel: bool, max_workers: int = 2):
    """运行基准测试"""
    print(f"\n--- {mode}模式测试 ({num_samples}样本) ---")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data = create_test_config(enable_parallel, max_workers)
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print(f"开始生成 {num_samples} 个样本...")
            start_time = time.time()
            generator.generate(num_samples)
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time = total_time / num_samples
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            image_count = len(list(images_dir.glob("*.png"))) if images_dir.exists() else 0
            annotation_count = len(list(annotations_dir.glob("*.json"))) if annotations_dir.exists() else 0
            
            print(f"✅ {mode}模式完成:")
            print(f"   总耗时: {total_time:.2f}秒")
            print(f"   平均每样本: {avg_time:.2f}秒")
            print(f"   样本/秒: {num_samples/total_time:.2f}")
            print(f"   生成图像: {image_count} 个")
            print(f"   生成标注: {annotation_count} 个")
            
            success = image_count == num_samples and annotation_count == num_samples
            
            return {
                'mode': mode,
                'num_samples': num_samples,
                'total_time': total_time,
                'avg_time': avg_time,
                'samples_per_second': num_samples / total_time,
                'image_count': image_count,
                'annotation_count': annotation_count,
                'success': success
            }
            
        except Exception as e:
            print(f"❌ {mode}模式测试失败: {e}")
            return {
                'mode': mode,
                'num_samples': num_samples,
                'total_time': 0,
                'avg_time': 0,
                'samples_per_second': 0,
                'image_count': 0,
                'annotation_count': 0,
                'success': False,
                'error': str(e)
            }


def main():
    """主函数"""
    print("TableRender V5.2 简化性能基准测试")
    print("=" * 50)
    
    # 测试参数
    test_samples = [4, 6]  # 测试样本数量
    
    results = []
    
    for num_samples in test_samples:
        print(f"\n🔄 测试 {num_samples} 个样本的性能...")
        
        # 1. 串行模式测试
        serial_result = run_benchmark_test("串行", num_samples, False, 1)
        results.append(serial_result)
        
        # 2. 并行模式测试（2线程）
        parallel_2_result = run_benchmark_test("并行(2线程)", num_samples, True, 2)
        results.append(parallel_2_result)
        
        # 3. 并行模式测试（4线程，如果样本数足够）
        if num_samples >= 4:
            parallel_4_result = run_benchmark_test("并行(4线程)", num_samples, True, 4)
            results.append(parallel_4_result)
        
        # 计算加速比
        if serial_result['success'] and parallel_2_result['success']:
            speedup_2 = serial_result['total_time'] / parallel_2_result['total_time']
            print(f"\n📈 {num_samples}样本性能对比:")
            print(f"   串行模式: {serial_result['total_time']:.2f}秒")
            print(f"   并行(2线程): {parallel_2_result['total_time']:.2f}秒")
            print(f"   加速比: {speedup_2:.2f}x")
            
            if num_samples >= 4 and 'parallel_4_result' in locals() and parallel_4_result['success']:
                speedup_4 = serial_result['total_time'] / parallel_4_result['total_time']
                print(f"   并行(4线程): {parallel_4_result['total_time']:.2f}秒")
                print(f"   4线程加速比: {speedup_4:.2f}x")
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 性能基准测试总结")
    print("=" * 50)
    
    print(f"{'模式':<15} {'样本数':<8} {'总时间(s)':<12} {'平均时间(s)':<12} {'样本/秒':<10} {'状态':<8}")
    print("-" * 70)
    
    for result in results:
        status = "✅成功" if result['success'] else "❌失败"
        print(f"{result['mode']:<15} {result['num_samples']:<8} "
              f"{result['total_time']:<12.2f} {result['avg_time']:<12.2f} "
              f"{result['samples_per_second']:<10.2f} {status:<8}")
    
    # 计算整体性能提升
    serial_results = [r for r in results if r['mode'] == '串行' and r['success']]
    parallel_results = [r for r in results if '并行' in r['mode'] and r['success']]
    
    if serial_results and parallel_results:
        # 计算平均加速比
        speedups = []
        for serial in serial_results:
            for parallel in parallel_results:
                if serial['num_samples'] == parallel['num_samples']:
                    speedup = serial['total_time'] / parallel['total_time']
                    speedups.append(speedup)
        
        if speedups:
            avg_speedup = sum(speedups) / len(speedups)
            max_speedup = max(speedups)
            
            print(f"\n🚀 V5.2性能优化效果:")
            print(f"   平均加速比: {avg_speedup:.2f}x")
            print(f"   最大加速比: {max_speedup:.2f}x")
            
            if avg_speedup >= 1.5:
                print("✅ V5.2并行优化效果显著！")
            elif avg_speedup >= 1.2:
                print("⚠️  V5.2并行优化有一定效果")
            else:
                print("❌ V5.2并行优化效果不明显")
    
    # 检查是否有失败的测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n⚠️  有 {len(failed_tests)} 个测试失败:")
        for failed in failed_tests:
            error_msg = failed.get('error', 'Unknown error')
            print(f"   - {failed['mode']} ({failed['num_samples']}样本): {error_msg}")
    
    # 总体评估
    success_rate = len([r for r in results if r['success']]) / len(results)
    
    print(f"\n📋 测试完成:")
    print(f"   总测试数: {len(results)}")
    print(f"   成功率: {success_rate:.1%}")
    
    if success_rate >= 0.8:
        print("🎉 V5.2性能基准测试基本通过！")
        return True
    else:
        print("💥 V5.2性能基准测试存在问题！")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
