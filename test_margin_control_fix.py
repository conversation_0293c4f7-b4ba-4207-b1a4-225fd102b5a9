#!/usr/bin/env python3
"""
测试margin_control修复

验证margin_control功能是否在所有生成模式下都被正确调用。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_margin_control_config_loading():
    """测试margin_control配置加载"""
    print("=== 测试margin_control配置加载 ===")
    
    # 创建包含margin_control的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 3,
            'cols': 4,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_background': True,
            'background_image_path': None,  # 将在运行时解析
            'margin_control': {
                'range_list': [[30, 60], [60, 100]],
                'probability_list': [0.7, 0.3]
            }
        },
        'performance': {
            'enable_parallel': False,
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    try:
        config = RenderConfig(**config_data)
        print("✅ 配置加载成功")
        
        if config.postprocessing and config.postprocessing.margin_control:
            margin_config = config.postprocessing.margin_control
            print(f"   - margin_control存在: {margin_config is not None}")
            print(f"   - range_list: {margin_config.range_list}")
            print(f"   - probability_list: {margin_config.probability_list}")
            return True
        else:
            print("❌ margin_control配置缺失")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_serial_generation_with_margin_control():
    """测试串行生成中的margin_control调用"""
    print("\n=== 测试串行生成中的margin_control调用 ===")
    
    # 创建包含margin_control的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_background': False,  # 简化测试，不使用背景
            'apply_perspective': False,  # 简化测试，不使用透视变换
            'margin_control': {
                'range_list': [[20, 40], [40, 80]],
                'probability_list': [0.5, 0.5]
            }
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 串行MainGenerator创建成功")
            
            # 测试单个样本生成
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成（包含margin_control）...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 图像大小: {result['image_size_bytes']} bytes")
            
            # 检查生成的图像文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            
            if images_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                if image_files:
                    image_file = image_files[0]
                    # 检查图像尺寸
                    from PIL import Image
                    with Image.open(image_file) as img:
                        width, height = img.size
                        print(f"   - 生成图像尺寸: {width}x{height}")
                        
                        # 如果margin_control工作正常，图像应该不会太大
                        # 这里设置一个合理的阈值进行检查
                        if width > 3000 or height > 3000:
                            print("⚠️  图像尺寸较大，可能margin_control未生效")
                            return False
                        else:
                            print("✅ 图像尺寸合理，margin_control可能已生效")
                            return True
                else:
                    print("❌ 未找到生成的图像文件")
                    return False
            else:
                print("❌ 图像输出目录不存在")
                return False
            
        except Exception as e:
            print(f"❌ 串行生成测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_generation_with_margin_control():
    """测试并行生成中的margin_control调用"""
    print("\n=== 测试并行生成中的margin_control调用 ===")
    
    # 创建包含margin_control的并行测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_background': False,  # 简化测试，不使用背景
            'apply_perspective': False,  # 简化测试，不使用透视变换
            'margin_control': {
                'range_list': [[30, 50], [50, 80]],
                'probability_list': [0.6, 0.4]
            }
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行MainGenerator创建成功")
            
            # 测试并行生成
            print("开始测试并行生成（包含margin_control）...")
            generator.generate(3)  # 生成3个样本
            
            print("✅ 并行生成成功")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                if len(image_files) == 3 and len(annotation_files) == 3:
                    # 检查第一个图像的尺寸
                    from PIL import Image
                    with Image.open(image_files[0]) as img:
                        width, height = img.size
                        print(f"   - 第一个图像尺寸: {width}x{height}")
                        
                        # 检查尺寸是否合理
                        if width > 3000 or height > 3000:
                            print("⚠️  图像尺寸较大，可能margin_control未生效")
                            return False
                        else:
                            print("✅ 图像尺寸合理，margin_control可能已生效")
                            return True
                else:
                    print("❌ 生成的文件数量不正确")
                    return False
            else:
                print("❌ 输出目录不存在")
                return False
            
        except Exception as e:
            print(f"❌ 并行生成测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_margin_control_method_exists():
    """测试margin_control相关方法是否存在"""
    print("\n=== 测试margin_control相关方法是否存在 ===")
    
    try:
        config_data = {
            'structure': {
                'header_rows': 1,
                'body_rows': 2,
                'cols': 3
            }
        }
        
        config = RenderConfig(**config_data)
        generator = MainGenerator(config, debug_mode=False)
        
        # 检查关键方法是否存在
        methods_to_check = [
            '_apply_margin_control_crop',
            '_calculate_margin_control_crop_box',
            '_select_margin_from_config',
            '_calculate_crop_box'
        ]
        
        for method_name in methods_to_check:
            if hasattr(generator, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 方法检查失败: {e}")
        return False


def main():
    """主函数"""
    print("TableRender V5.2 margin_control修复测试")
    print("=" * 50)
    
    # 测试配置加载
    config_success = test_margin_control_config_loading()
    
    # 测试方法存在性
    method_success = test_margin_control_method_exists()
    
    # 测试串行生成
    serial_success = test_serial_generation_with_margin_control()
    
    # 测试并行生成
    parallel_success = test_parallel_generation_with_margin_control()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"配置加载: {'✅ 通过' if config_success else '❌ 失败'}")
    print(f"方法存在性: {'✅ 通过' if method_success else '❌ 失败'}")
    print(f"串行生成: {'✅ 通过' if serial_success else '❌ 失败'}")
    print(f"并行生成: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = config_success and method_success and serial_success and parallel_success
    
    if all_success:
        print("\n🎉 所有测试通过！margin_control功能已修复")
        print("现在所有生成模式都会正确调用margin_control处理")
        
        print("\n📋 修复总结:")
        print("1. ✅ 单个样本生成方法中添加了margin_control调用")
        print("2. ✅ 传统模式中添加了margin_control调用")
        print("3. ✅ 保持了与原有CSS模式的一致性")
        print("4. ✅ 串行和并行模式都支持margin_control")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
