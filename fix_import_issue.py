#!/usr/bin/env python3
"""
修复导入问题
"""

import os
import sys
import shutil
from pathlib import Path

def fix_import_issue():
    """修复导入问题"""
    print("=== 修复导入问题 ===")
    
    # 1. 检查并删除可能存在的config目录
    config_dir = Path("table_render/config")
    if config_dir.exists():
        print(f"发现config目录: {config_dir}")
        print(f"目录内容: {list(config_dir.iterdir())}")
        
        try:
            shutil.rmtree(config_dir)
            print("✅ 已删除config目录")
        except Exception as e:
            print(f"❌ 删除config目录失败: {e}")
            return False
    else:
        print("✅ config目录不存在")
    
    # 2. 检查config.py文件是否存在
    config_file = Path("table_render/config.py")
    if config_file.exists():
        print("✅ config.py文件存在")
    else:
        print("❌ config.py文件不存在")
        return False
    
    # 3. 清理Python缓存
    print("清理Python缓存...")
    for root, dirs, files in os.walk("table_render"):
        # 删除__pycache__目录
        if "__pycache__" in dirs:
            pycache_dir = Path(root) / "__pycache__"
            try:
                shutil.rmtree(pycache_dir)
                print(f"删除缓存: {pycache_dir}")
            except Exception as e:
                print(f"删除缓存失败: {e}")
        
        # 删除.pyc文件
        for file in files:
            if file.endswith('.pyc'):
                pyc_file = Path(root) / file
                try:
                    pyc_file.unlink()
                    print(f"删除缓存文件: {pyc_file}")
                except Exception as e:
                    print(f"删除缓存文件失败: {e}")
    
    # 4. 测试导入
    print("\n测试导入...")
    try:
        sys.path.insert(0, '.')
        
        # 重新加载模块
        if 'table_render.config' in sys.modules:
            del sys.modules['table_render.config']
        if 'table_render.utils.parallel_generator' in sys.modules:
            del sys.modules['table_render.utils.parallel_generator']
        if 'table_render.main_generator' in sys.modules:
            del sys.modules['table_render.main_generator']
        
        from table_render.config import RenderConfig
        print("✅ RenderConfig导入成功")
        
        from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
        print("✅ TurboJPEGSaver导入成功")
        
        from table_render.utils.parallel_generator import ParallelGenerator
        print("✅ ParallelGenerator导入成功")
        
        from table_render.main_generator import MainGenerator
        print("✅ MainGenerator导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_import_issue()
    if success:
        print("\n✅ 导入问题修复成功！")
        print("现在可以正常运行TableRender了")
    else:
        print("\n❌ 导入问题修复失败！")
        print("请检查错误信息并手动修复")
    
    sys.exit(0 if success else 1)
