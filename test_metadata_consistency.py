#!/usr/bin/env python3
"""
测试metadata一致性和完整性

验证串行生成和并行生成的metadata格式是否一致，以及是否包含完整的配置信息。
"""

import sys
import tempfile
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_metadata_structure():
    """测试metadata结构的完整性"""
    print("=== 测试metadata结构的完整性 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.1,
            'max_row_span': 2,
            'max_col_span': 2
        },
        'content': {
            'source_type': 'random_text',
            'text_length_range': [5, 15],
            'number_probability': 0.3
        },
        'style': {
            'header': {
                'background_color': ['#f0f0f0', '#e0e0e0'],
                'text_color': '#000000',
                'font_weight': 'bold'
            },
            'body': {
                'background_color': '#ffffff',
                'text_color': '#333333'
            }
        },
        'postprocessing': {
            'apply_perspective': True,
            'perspective_offset_ratio': [0.01, 0.03],
            'apply_background': False,
            'apply_degradation_blur': True,
            'degradation_blur_kernel_size': [1, 2],
            'degradation_blur_sigma': [0.5, 1.0]
        },
        'performance': {
            'enable_parallel': False,
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 95,
            'turbo_jpeg_format': 'jpeg'
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ MainGenerator创建成功")
            
            # 生成一个样本
            generator.generate(1)
            
            # 读取生成的metadata文件
            annotations_dir = Path(temp_dir) / "annotations"
            metadata_files = list(annotations_dir.glob("*.json"))
            
            if not metadata_files:
                print("❌ 未找到metadata文件")
                return False
            
            # 读取第一个metadata文件
            with open(metadata_files[0], 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            print("✅ metadata文件读取成功")
            
            # 检查必需的顶级字段
            required_fields = [
                'resolved_params',
                'original_config', 
                'sample_seed',
                'sample_index',
                'generation_timestamp',
                'turbo_jpeg_used'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in metadata:
                    missing_fields.append(field)
                else:
                    print(f"✅ 字段 '{field}' 存在")
            
            if missing_fields:
                print(f"❌ 缺少字段: {missing_fields}")
                return False
            
            # 检查resolved_params的完整性
            resolved_params = metadata.get('resolved_params', {})
            required_resolved_sections = ['structure', 'content', 'style', 'output', 'postprocessing']
            
            missing_sections = []
            for section in required_resolved_sections:
                if section not in resolved_params:
                    missing_sections.append(section)
                else:
                    print(f"✅ resolved_params.{section} 存在")
            
            if missing_sections:
                print(f"❌ resolved_params缺少部分: {missing_sections}")
                return False
            
            # 检查original_config的完整性
            original_config = metadata.get('original_config', {})
            required_config_sections = ['structure', 'content', 'style', 'output', 'performance']
            
            missing_config_sections = []
            for section in required_config_sections:
                if section not in original_config:
                    missing_config_sections.append(section)
                else:
                    print(f"✅ original_config.{section} 存在")
            
            if missing_config_sections:
                print(f"❌ original_config缺少部分: {missing_config_sections}")
                return False
            
            print(f"✅ metadata结构完整，总大小: {len(json.dumps(metadata))} 字符")
            return True
            
        except Exception as e:
            print(f"❌ metadata结构测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_serial_parallel_consistency():
    """测试串行和并行生成的metadata一致性"""
    print("\n=== 测试串行和并行生成的metadata一致性 ===")
    
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0  # 禁用合并以确保一致性
        },
        'content': {
            'source_type': 'random_text',
            'text_length_range': [8, 12]
        },
        'performance': {
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 90
        },
        'seed': 12345  # 固定种子确保可比较性
    }
    
    serial_metadata = None
    parallel_metadata = None
    
    # 测试串行生成
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        config_data['performance']['enable_parallel'] = False
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            generator.generate(1)
            
            # 读取串行生成的metadata
            annotations_dir = Path(temp_dir) / "annotations"
            metadata_files = list(annotations_dir.glob("*.json"))
            
            if metadata_files:
                with open(metadata_files[0], 'r', encoding='utf-8') as f:
                    serial_metadata = json.load(f)
                print("✅ 串行生成metadata读取成功")
            else:
                print("❌ 串行生成未找到metadata文件")
                return False
                
        except Exception as e:
            print(f"❌ 串行生成失败: {e}")
            return False
    
    # 测试并行生成
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        config_data['performance']['enable_parallel'] = True
        config_data['performance']['max_workers'] = 1  # 使用单线程确保一致性
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            generator.generate(1)
            
            # 读取并行生成的metadata
            annotations_dir = Path(temp_dir) / "annotations"
            metadata_files = list(annotations_dir.glob("*.json"))
            
            if metadata_files:
                with open(metadata_files[0], 'r', encoding='utf-8') as f:
                    parallel_metadata = json.load(f)
                print("✅ 并行生成metadata读取成功")
            else:
                print("❌ 并行生成未找到metadata文件")
                return False
                
        except Exception as e:
            print(f"❌ 并行生成失败: {e}")
            return False
    
    # 比较metadata结构
    if not serial_metadata or not parallel_metadata:
        print("❌ 无法获取完整的metadata进行比较")
        return False
    
    # 检查字段一致性
    serial_keys = set(serial_metadata.keys())
    parallel_keys = set(parallel_metadata.keys())
    
    if serial_keys == parallel_keys:
        print("✅ 串行和并行生成的metadata字段完全一致")
        print(f"   共同字段: {sorted(serial_keys)}")
    else:
        only_serial = serial_keys - parallel_keys
        only_parallel = parallel_keys - serial_keys
        print(f"❌ metadata字段不一致")
        if only_serial:
            print(f"   仅串行有: {only_serial}")
        if only_parallel:
            print(f"   仅并行有: {only_parallel}")
        return False
    
    # 检查resolved_params结构一致性
    serial_resolved = serial_metadata.get('resolved_params', {})
    parallel_resolved = parallel_metadata.get('resolved_params', {})
    
    serial_resolved_keys = set(serial_resolved.keys())
    parallel_resolved_keys = set(parallel_resolved.keys())
    
    if serial_resolved_keys == parallel_resolved_keys:
        print("✅ resolved_params结构一致")
        print(f"   resolved_params部分: {sorted(serial_resolved_keys)}")
    else:
        print("❌ resolved_params结构不一致")
        return False
    
    # 检查original_config结构一致性
    serial_config = serial_metadata.get('original_config', {})
    parallel_config = parallel_metadata.get('original_config', {})
    
    serial_config_keys = set(serial_config.keys())
    parallel_config_keys = set(parallel_config.keys())
    
    if serial_config_keys == parallel_config_keys:
        print("✅ original_config结构一致")
        print(f"   original_config部分: {sorted(serial_config_keys)}")
    else:
        print("❌ original_config结构不一致")
        return False
    
    return True


def test_metadata_completeness_for_restoration():
    """测试metadata是否足够完整以支持表格还原"""
    print("\n=== 测试metadata完整性（表格还原支持） ===")
    
    config_data = {
        'structure': {
            'header_rows': 2,
            'body_rows': 3,
            'cols': 4,
            'merge_probability': 0.2,
            'max_row_span': 2,
            'max_col_span': 3
        },
        'content': {
            'source_type': 'random_text',
            'text_length_range': [3, 20],
            'number_probability': 0.4
        },
        'style': {
            'header': {
                'background_color': ['#f0f0f0', '#e0e0e0', '#d0d0d0'],
                'text_color': ['#000000', '#333333'],
                'font_weight': ['bold', 'normal'],
                'font_size': [12, 14, 16]
            },
            'body': {
                'background_color': ['#ffffff', '#f9f9f9'],
                'text_color': ['#333333', '#666666'],
                'font_size': [10, 12, 14]
            },
            'border_mode': 'partial',
            'zebra_stripes': {
                'probability': 0.5,
                'colors': ['#f0f0f0', '#ffffff']
            }
        },
        'postprocessing': {
            'apply_perspective': True,
            'perspective_offset_ratio': [0.02, 0.05],
            'apply_background': False,
            'apply_degradation_blur': True,
            'degradation_blur_kernel_size': [1, 3],
            'degradation_blur_sigma': [0.3, 1.2],
            'apply_degradation_noise': True,
            'degradation_noise_intensity': [0.01, 0.03]
        },
        'performance': {
            'enable_parallel': True,
            'max_workers': 2,
            'enable_turbo_jpeg': True,
            'turbo_jpeg_quality': 92
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            # 生成一个复杂样本
            generator.generate(1)
            
            # 读取metadata
            annotations_dir = Path(temp_dir) / "annotations"
            metadata_files = list(annotations_dir.glob("*.json"))
            
            if not metadata_files:
                print("❌ 未找到metadata文件")
                return False
            
            with open(metadata_files[0], 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            print("✅ 复杂样本metadata读取成功")
            
            # 检查表格还原所需的关键信息
            restoration_checks = []
            
            # 1. 结构信息
            resolved_structure = metadata.get('resolved_params', {}).get('structure', {})
            if all(key in resolved_structure for key in ['header_rows', 'body_rows', 'cols', 'merge_probability']):
                restoration_checks.append(('结构参数', True))
                print("✅ 结构参数完整")
            else:
                restoration_checks.append(('结构参数', False))
                print("❌ 结构参数不完整")
            
            # 2. 内容信息
            resolved_content = metadata.get('resolved_params', {}).get('content', {})
            if 'source_type' in resolved_content:
                restoration_checks.append(('内容参数', True))
                print("✅ 内容参数存在")
            else:
                restoration_checks.append(('内容参数', False))
                print("❌ 内容参数缺失")
            
            # 3. 样式信息
            resolved_style = metadata.get('resolved_params', {}).get('style', {})
            if all(key in resolved_style for key in ['header', 'body']):
                restoration_checks.append(('样式参数', True))
                print("✅ 样式参数完整")
            else:
                restoration_checks.append(('样式参数', False))
                print("❌ 样式参数不完整")
            
            # 4. 后处理信息
            resolved_postprocessing = metadata.get('resolved_params', {}).get('postprocessing', {})
            if resolved_postprocessing:
                restoration_checks.append(('后处理参数', True))
                print("✅ 后处理参数存在")
            else:
                restoration_checks.append(('后处理参数', False))
                print("❌ 后处理参数缺失")
            
            # 5. 种子信息
            if 'sample_seed' in metadata:
                restoration_checks.append(('种子信息', True))
                print("✅ 种子信息存在")
            else:
                restoration_checks.append(('种子信息', False))
                print("❌ 种子信息缺失")
            
            # 6. 原始配置
            if 'original_config' in metadata:
                restoration_checks.append(('原始配置', True))
                print("✅ 原始配置存在")
            else:
                restoration_checks.append(('原始配置', False))
                print("❌ 原始配置缺失")
            
            # 统计结果
            passed_checks = sum(1 for _, passed in restoration_checks if passed)
            total_checks = len(restoration_checks)
            
            print(f"\n还原支持检查结果: {passed_checks}/{total_checks} 通过")
            
            if passed_checks == total_checks:
                print("✅ metadata完全支持表格还原")
                return True
            else:
                print("❌ metadata不足以完全支持表格还原")
                return False
            
        except Exception as e:
            print(f"❌ 完整性测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数"""
    print("TableRender V5.2 metadata一致性和完整性测试")
    print("=" * 60)
    
    # 测试metadata结构
    structure_success = test_metadata_structure()
    
    # 测试串行并行一致性
    consistency_success = test_serial_parallel_consistency()
    
    # 测试还原支持完整性
    restoration_success = test_metadata_completeness_for_restoration()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"metadata结构完整性: {'✅ 通过' if structure_success else '❌ 失败'}")
    print(f"串行并行一致性: {'✅ 通过' if consistency_success else '❌ 失败'}")
    print(f"表格还原支持: {'✅ 通过' if restoration_success else '❌ 失败'}")
    
    all_success = structure_success and consistency_success and restoration_success
    
    if all_success:
        print("\n🎉 所有测试通过！metadata格式已统一且完整")
        print("现在可以通过metadata完整还原表格配置和生成过程")
        
        print("\n📋 metadata包含的完整信息:")
        print("1. ✅ resolved_params - 所有解析后的具体参数")
        print("2. ✅ original_config - 原始配置文件内容")
        print("3. ✅ sample_seed - 样本随机种子")
        print("4. ✅ sample_index - 样本索引")
        print("5. ✅ generation_timestamp - 生成时间戳")
        print("6. ✅ turbo_jpeg_used - TurboJPEG使用状态")
        print("7. ✅ csv_sampling_info - CSV采样信息（如适用）")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
