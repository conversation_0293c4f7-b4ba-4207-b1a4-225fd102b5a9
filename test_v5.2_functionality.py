#!/usr/bin/env python3
"""
TableRender V5.2 功能验证脚本

验证并行生成和TurboJPEG优化的功能正确性。
"""

import os
import sys
import yaml
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator
from table_render.utils.turbo_jpeg_saver import TurboJPEGSaver
from table_render.utils.parallel_generator import ParallelGenerator


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def test_config_loading():
    """测试配置加载"""
    logger = logging.getLogger(__name__)
    logger.info("测试配置加载...")
    
    try:
        # 测试并行配置
        with open("configs/v5.2_test_parallel.yaml", 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        config = RenderConfig(**config_data)
        
        # 验证性能配置
        assert config.performance is not None, "性能配置不应为空"
        assert config.performance.enable_parallel == True, "应启用并行模式"
        assert config.performance.max_workers == 2, "工作线程数应为2"
        
        logger.info("✅ 配置加载测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置加载测试失败: {e}")
        return False


def test_turbo_jpeg_saver():
    """测试TurboJPEG保存器"""
    logger = logging.getLogger(__name__)
    logger.info("测试TurboJPEG保存器...")
    
    try:
        # 创建保存器
        saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        
        # 获取统计信息
        stats = saver.get_stats()
        logger.info(f"TurboJPEG可用: {stats['turbo_available']}")
        logger.info(f"质量设置: {stats['quality']}")
        
        # 创建测试图像
        from PIL import Image
        import numpy as np
        
        # 创建一个简单的测试图像
        test_image = Image.fromarray(
            np.random.randint(0, 256, (100, 100, 3), dtype=np.uint8)
        )
        
        # 测试保存
        test_output_dir = Path("./test_output")
        test_output_dir.mkdir(exist_ok=True)
        
        test_path = test_output_dir / "turbo_test.jpg"
        success = saver.save_image(test_image, test_path, format_hint="jpeg")
        
        if success and test_path.exists():
            logger.info("✅ TurboJPEG保存器测试通过")
            # 清理测试文件
            test_path.unlink()
            return True
        else:
            logger.error("❌ TurboJPEG保存失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ TurboJPEG保存器测试失败: {e}")
        return False


def test_parallel_generator():
    """测试并行生成器"""
    logger = logging.getLogger(__name__)
    logger.info("测试并行生成器...")
    
    try:
        # 加载测试配置
        with open("configs/v5.2_test_parallel.yaml", 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        config = RenderConfig(**config_data)
        
        # 创建并行生成器
        parallel_generator = ParallelGenerator(
            config=config,
            max_workers=2,
            enable_turbo_jpeg=True
        )
        
        # 测试最优线程数计算
        optimal_workers = parallel_generator.get_optimal_worker_count()
        logger.info(f"建议线程数: {optimal_workers}")
        
        # 测试时间估算
        estimation = parallel_generator.estimate_completion_time(4, 0, 0)
        logger.info(f"预估总时间: {estimation['estimated_total_time']:.2f}秒")
        
        logger.info("✅ 并行生成器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 并行生成器测试失败: {e}")
        return False


def test_main_generator_integration():
    """测试主生成器集成"""
    logger = logging.getLogger(__name__)
    logger.info("测试主生成器集成...")
    
    try:
        # 测试串行模式
        logger.info("测试串行模式...")
        with open("configs/v5.2_test_serial.yaml", 'r', encoding='utf-8') as f:
            serial_config_data = yaml.safe_load(f)
        
        serial_config = RenderConfig(**serial_config_data)
        serial_generator = MainGenerator(serial_config, debug_mode=False)
        
        # 生成1个样本
        serial_generator.generate(1)
        logger.info("串行模式测试完成")
        
        # 测试并行模式
        logger.info("测试并行模式...")
        with open("configs/v5.2_test_parallel.yaml", 'r', encoding='utf-8') as f:
            parallel_config_data = yaml.safe_load(f)
        
        parallel_config = RenderConfig(**parallel_config_data)
        parallel_generator = MainGenerator(parallel_config, debug_mode=False)
        
        # 生成2个样本
        parallel_generator.generate(2)
        logger.info("并行模式测试完成")
        
        logger.info("✅ 主生成器集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 主生成器集成测试失败: {e}")
        return False


def verify_output_files():
    """验证输出文件"""
    logger = logging.getLogger(__name__)
    logger.info("验证输出文件...")
    
    try:
        # 检查串行输出
        serial_output_dir = Path("./output/v5.2_serial_test")
        if serial_output_dir.exists():
            images_dir = serial_output_dir / "images"
            annotations_dir = serial_output_dir / "annotations"
            
            image_files = list(images_dir.glob("*.png")) if images_dir.exists() else []
            annotation_files = list(annotations_dir.glob("*.json")) if annotations_dir.exists() else []
            
            logger.info(f"串行模式生成文件: {len(image_files)}个图像, {len(annotation_files)}个标注")
        
        # 检查并行输出
        parallel_output_dir = Path("./output/v5.2_parallel_test")
        if parallel_output_dir.exists():
            images_dir = parallel_output_dir / "images"
            annotations_dir = parallel_output_dir / "annotations"
            
            image_files = list(images_dir.glob("*.png")) if images_dir.exists() else []
            annotation_files = list(annotations_dir.glob("*.json")) if annotations_dir.exists() else []
            
            logger.info(f"并行模式生成文件: {len(image_files)}个图像, {len(annotation_files)}个标注")
        
        logger.info("✅ 输出文件验证完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 输出文件验证失败: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("TableRender V5.2 功能验证开始")
    
    # 运行所有测试
    tests = [
        ("配置加载", test_config_loading),
        ("TurboJPEG保存器", test_turbo_jpeg_saver),
        ("并行生成器", test_parallel_generator),
        ("主生成器集成", test_main_generator_integration),
        ("输出文件验证", verify_output_files)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info(f"功能验证总结: {passed_tests}/{total_tests} 测试通过")
    logger.info(f"{'='*60}")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有功能验证通过！")
        return 0
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
