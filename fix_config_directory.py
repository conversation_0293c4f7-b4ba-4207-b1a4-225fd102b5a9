#!/usr/bin/env python3
"""
修复配置目录问题

删除空的table_render/config目录，解决导入冲突问题。
"""

import os
import shutil
from pathlib import Path


def fix_config_directory():
    """修复配置目录问题"""
    print("修复配置目录问题...")
    
    # 检查config目录
    config_dir = Path("table_render/config")
    
    if config_dir.exists():
        print(f"发现config目录: {config_dir}")
        
        # 检查目录是否为空
        if config_dir.is_dir():
            files = list(config_dir.iterdir())
            if not files:
                print("目录为空，正在删除...")
                try:
                    config_dir.rmdir()
                    print("✅ 空的config目录已删除")
                except OSError as e:
                    print(f"❌ 删除目录失败: {e}")
                    print("请手动删除table_render/config目录")
                    return False
            else:
                print(f"目录不为空，包含文件: {[f.name for f in files]}")
                print("请检查这些文件是否需要保留")
                return False
    else:
        print("config目录不存在，无需修复")
    
    # 验证config.py文件存在
    config_file = Path("table_render/config.py")
    if config_file.exists():
        print("✅ config.py文件存在")
    else:
        print("❌ config.py文件不存在！")
        return False
    
    # 测试导入
    print("测试导入...")
    try:
        import sys
        sys.path.insert(0, '.')
        from table_render.config import RenderConfig, PerformanceConfig
        print("✅ 导入测试成功")
        return True
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        return False


if __name__ == "__main__":
    success = fix_config_directory()
    if success:
        print("\n🎉 配置目录问题修复成功！")
        print("现在可以正常运行TableRender了")
    else:
        print("\n💥 配置目录问题修复失败！")
        print("请手动删除table_render/config目录（如果存在且为空）")
