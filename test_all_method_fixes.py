#!/usr/bin/env python3
"""
测试所有方法调用修复

验证所有不存在的方法调用是否已修复。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_html_renderer_methods():
    """测试HtmlRenderer方法调用"""
    print("=== 测试HtmlRenderer方法调用 ===")
    
    try:
        from table_render.renderers.html_renderer import HtmlRenderer
        
        # 检查方法是否存在
        methods_to_check = [
            'create_async',
            'close_async', 
            'render'
        ]
        
        for method_name in methods_to_check:
            if hasattr(HtmlRenderer, method_name):
                print(f"✅ HtmlRenderer.{method_name} 方法存在")
            else:
                print(f"❌ HtmlRenderer.{method_name} 方法不存在")
                return False
        
        # 检查不应该存在的方法
        wrong_methods = [
            'render_with_background_async'
        ]
        
        for method_name in wrong_methods:
            if hasattr(HtmlRenderer, method_name):
                print(f"❌ HtmlRenderer.{method_name} 方法不应该存在")
                return False
            else:
                print(f"✅ HtmlRenderer.{method_name} 方法正确不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ HtmlRenderer方法检查失败: {e}")
        return False


def test_annotation_converter_methods():
    """测试AnnotationConverter方法调用"""
    print("\n=== 测试AnnotationConverter方法调用 ===")
    
    try:
        from table_render.utils.annotation_converter import AnnotationConverter
        
        converter = AnnotationConverter()
        
        # 检查正确的方法
        correct_methods = [
            'convert_to_final_format'
        ]
        
        for method_name in correct_methods:
            if hasattr(converter, method_name):
                print(f"✅ AnnotationConverter.{method_name} 方法存在")
            else:
                print(f"❌ AnnotationConverter.{method_name} 方法不存在")
                return False
        
        # 检查不应该存在的方法
        wrong_methods = [
            'convert_to_serializable'
        ]
        
        for method_name in wrong_methods:
            if hasattr(converter, method_name):
                print(f"❌ AnnotationConverter.{method_name} 方法不应该存在")
                return False
            else:
                print(f"✅ AnnotationConverter.{method_name} 方法正确不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ AnnotationConverter方法检查失败: {e}")
        return False


def test_serial_generation_with_methods():
    """测试串行生成中的方法调用"""
    print("\n=== 测试串行生成中的方法调用 ===")
    
    # 创建简单的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 串行MainGenerator创建成功")
            
            # 测试单个样本生成（这会调用所有修复的方法）
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            
            return True
            
        except AttributeError as e:
            if "object has no attribute" in str(e):
                print(f"❌ 仍然存在方法调用错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_parallel_generation_with_methods():
    """测试并行生成中的方法调用"""
    print("\n=== 测试并行生成中的方法调用 ===")
    
    # 创建并行测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'performance': {
            'enable_parallel': True,  # 启用并行模式
            'max_workers': 2,
            'max_browser_instances': 2
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 并行MainGenerator创建成功")
            
            # 测试并行生成
            print("开始测试并行生成...")
            generator.generate(3)  # 生成3个样本
            
            print("✅ 并行生成成功")
            
            # 检查输出文件
            output_path = Path(temp_dir)
            images_dir = output_path / "images"
            annotations_dir = output_path / "annotations"
            
            if images_dir.exists() and annotations_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                annotation_files = list(annotations_dir.glob("*.json"))
                
                print(f"   - 生成图像文件: {len(image_files)} 个")
                print(f"   - 生成标注文件: {len(annotation_files)} 个")
                
                return len(image_files) == 3 and len(annotation_files) == 3
            else:
                print("❌ 输出目录不存在")
                return False
            
        except AttributeError as e:
            if "object has no attribute" in str(e):
                print(f"❌ 并行模式仍然存在方法调用错误: {e}")
                return False
            else:
                print(f"❌ 其他AttributeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 并行测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_method_signature_compatibility():
    """测试方法签名兼容性"""
    print("\n=== 测试方法签名兼容性 ===")
    
    try:
        from table_render.renderers.html_renderer import HtmlRenderer
        from table_render.utils.annotation_converter import AnnotationConverter
        import inspect
        
        # 检查HtmlRenderer.render方法签名
        render_signature = inspect.signature(HtmlRenderer.render)
        print(f"✅ HtmlRenderer.render 签名: {render_signature}")
        
        # 检查AnnotationConverter.convert_to_final_format方法签名
        converter = AnnotationConverter()
        convert_signature = inspect.signature(converter.convert_to_final_format)
        print(f"✅ AnnotationConverter.convert_to_final_format 签名: {convert_signature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {e}")
        return False


def main():
    """主函数"""
    print("TableRender V5.2 方法调用修复测试")
    print("=" * 50)
    
    # 测试方法存在性
    html_methods_success = test_html_renderer_methods()
    
    # 测试转换器方法
    converter_methods_success = test_annotation_converter_methods()
    
    # 测试方法签名
    signature_success = test_method_signature_compatibility()
    
    # 测试串行生成
    serial_success = test_serial_generation_with_methods()
    
    # 测试并行生成
    parallel_success = test_parallel_generation_with_methods()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"HtmlRenderer方法: {'✅ 通过' if html_methods_success else '❌ 失败'}")
    print(f"AnnotationConverter方法: {'✅ 通过' if converter_methods_success else '❌ 失败'}")
    print(f"方法签名兼容性: {'✅ 通过' if signature_success else '❌ 失败'}")
    print(f"串行生成测试: {'✅ 通过' if serial_success else '❌ 失败'}")
    print(f"并行生成测试: {'✅ 通过' if parallel_success else '❌ 失败'}")
    
    all_success = all([
        html_methods_success,
        converter_methods_success, 
        signature_success,
        serial_success,
        parallel_success
    ])
    
    if all_success:
        print("\n🎉 所有测试通过！方法调用问题已全部修复")
        print("现在可以正常使用 TableRender V5.2 的所有功能")
        
        print("\n📋 修复总结:")
        print("1. ✅ renderer.render_with_background_async() → renderer.render()")
        print("2. ✅ annotation_converter.convert_to_serializable() → convert_to_final_format()")
        print("3. ✅ 所有方法签名和参数传递正确")
        print("4. ✅ 串行和并行模式都能正常工作")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
