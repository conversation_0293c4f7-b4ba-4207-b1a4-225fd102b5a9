#!/usr/bin/env python3
"""
修复语法错误
"""

def fix_syntax_error():
    """修复语法错误"""
    print("修复语法错误...")
    
    # 读取当前文件内容
    with open("table_render/main_generator.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 找到正确的结束位置（第2663行的 return summary）
    correct_end = -1
    for i, line in enumerate(lines):
        if line.strip() == "return summary" and i > 2600:
            correct_end = i
            break
    
    if correct_end == -1:
        print("❌ 找不到正确的结束位置")
        return False
    
    print(f"找到正确的结束位置: 第{correct_end + 1}行")
    
    # 截断文件到正确的位置
    new_lines = lines[:correct_end + 1]
    
    # 写入新文件
    new_content = '\n'.join(new_lines)
    
    with open("table_render/main_generator.py", 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 语法错误修复完成")
    return True

if __name__ == "__main__":
    success = fix_syntax_error()
    if success:
        print("🎉 修复成功！")
        print("现在需要重新添加generate_sample方法")
    else:
        print("❌ 修复失败！")
