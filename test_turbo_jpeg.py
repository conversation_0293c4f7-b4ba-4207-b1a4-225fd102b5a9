#!/usr/bin/env python3
"""
TurboJPEG优化器测试脚本

用于验证TurboJPEG图像序列化优化器的功能和性能。
"""

import time
import tempfile
import numpy as np
from pathlib import Path
from PIL import Image

# 导入TurboJPEG优化器
from table_render.optimizers import TurboJPEGSaver


def create_test_image(width: int = 1920, height: int = 1080) -> Image.Image:
    """创建测试图像"""
    # 创建一个彩色测试图像
    image_array = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
    return Image.fromarray(image_array)


def test_turbo_jpeg_functionality():
    """测试TurboJPEG功能"""
    print("=== TurboJPEG功能测试 ===")
    
    # 创建测试图像
    test_image = create_test_image(800, 600)
    
    # 创建TurboJPEG保存器
    saver = TurboJPEGSaver(quality=95, enable_turbo=True)
    
    # 显示统计信息
    stats = saver.get_stats()
    print(f"TurboJPEG可用: {stats['turbo_available']}")
    print(f"质量设置: {stats['quality']}")
    print(f"TurboJPEG启用: {stats['enable_turbo']}")
    
    # 测试保存功能
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试JPEG保存
        jpeg_path = temp_path / "test.jpg"
        success = saver.save_image(test_image, jpeg_path, format_hint="jpeg")
        print(f"JPEG保存成功: {success}")
        print(f"JPEG文件大小: {jpeg_path.stat().st_size if jpeg_path.exists() else 0} bytes")
        
        # 测试PNG保存
        png_path = temp_path / "test.png"
        success = saver.save_image(test_image, png_path, format_hint="png")
        print(f"PNG保存成功: {success}")
        print(f"PNG文件大小: {png_path.stat().st_size if png_path.exists() else 0} bytes")
        
        # 测试字节数据保存
        from io import BytesIO
        img_bytes = BytesIO()
        test_image.save(img_bytes, format='PNG')
        img_bytes = img_bytes.getvalue()
        
        bytes_path = temp_path / "test_from_bytes.jpg"
        success = saver.save_image_bytes(img_bytes, bytes_path, format_hint="jpeg")
        print(f"字节数据保存成功: {success}")
        print(f"字节数据文件大小: {bytes_path.stat().st_size if bytes_path.exists() else 0} bytes")


def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 性能对比测试 ===")
    
    # 创建较大的测试图像
    test_image = create_test_image(1920, 1080)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试TurboJPEG性能
        turbo_saver = TurboJPEGSaver(quality=95, enable_turbo=True)
        turbo_times = []
        
        for i in range(5):
            start_time = time.time()
            turbo_path = temp_path / f"turbo_{i}.jpg"
            turbo_saver.save_image(test_image, turbo_path, format_hint="jpeg")
            end_time = time.time()
            turbo_times.append(end_time - start_time)
        
        avg_turbo_time = sum(turbo_times) / len(turbo_times)
        print(f"TurboJPEG平均保存时间: {avg_turbo_time:.4f}秒")
        
        # 测试PIL性能
        pil_saver = TurboJPEGSaver(quality=95, enable_turbo=False)
        pil_times = []
        
        for i in range(5):
            start_time = time.time()
            pil_path = temp_path / f"pil_{i}.jpg"
            pil_saver.save_image(test_image, pil_path, format_hint="jpeg")
            end_time = time.time()
            pil_times.append(end_time - start_time)
        
        avg_pil_time = sum(pil_times) / len(pil_times)
        print(f"PIL平均保存时间: {avg_pil_time:.4f}秒")
        
        # 计算加速比
        if avg_pil_time > 0:
            speedup = avg_pil_time / avg_turbo_time
            print(f"TurboJPEG加速比: {speedup:.2f}x")


def test_format_detection():
    """测试格式检测"""
    print("\n=== 格式检测测试 ===")
    
    test_image = create_test_image(400, 300)
    saver = TurboJPEGSaver()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试不同的文件扩展名
        test_cases = [
            ("test.jpg", "应该检测为JPEG"),
            ("test.jpeg", "应该检测为JPEG"),
            ("test.png", "应该检测为PNG"),
            ("test.unknown", "应该默认为JPEG")
        ]
        
        for filename, description in test_cases:
            file_path = temp_path / filename
            success = saver.save_image(test_image, file_path)
            print(f"{filename}: {description} - 保存{'成功' if success else '失败'}")


if __name__ == "__main__":
    try:
        test_turbo_jpeg_functionality()
        test_performance_comparison()
        test_format_detection()
        print("\n=== 所有测试完成 ===")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
