#!/usr/bin/env python3
"""
步骤1验证脚本：性能配置模型实现

验证PerformanceConfig是否正确集成到RenderConfig中。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from table_render.config import RenderConfig
    from table_render.config.performance_config import PerformanceConfig
    print("✅ 导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_basic_config():
    """测试基本配置功能"""
    print("\n🧪 测试基本配置功能...")
    
    config_data = {
        'structure': {
            'body_rows': 5,
            'cols': 4,
            'header_rows': 1
        },
        'performance': {
            'enable_parallel': True,
            'max_workers': 4,
            'max_browser_instances': 4
        }
    }
    
    try:
        config = RenderConfig(**config_data)
        perf = config.performance
        
        print(f"   - enable_parallel: {perf.enable_parallel}")
        print(f"   - max_workers: {perf.max_workers}")
        print(f"   - max_browser_instances: {perf.max_browser_instances}")
        print(f"   - 解析后的工作线程数: {perf.resolve_max_workers()}")
        print(f"   - 10个样本时使用线程数: {perf.get_actual_workers(10)}")
        
        print("✅ 基本配置测试通过")
        return True
    except Exception as e:
        print(f"❌ 基本配置测试失败: {e}")
        return False


def test_auto_config():
    """测试auto配置"""
    print("\n🧪 测试auto配置...")
    
    config_data = {
        'structure': {
            'body_rows': 5,
            'cols': 4,
            'header_rows': 1
        },
        'performance': {
            'enable_parallel': True,
            'max_workers': 'auto',
            'max_browser_instances': 8
        }
    }
    
    try:
        config = RenderConfig(**config_data)
        perf = config.performance
        
        print(f"   - max_workers: {perf.max_workers}")
        print(f"   - auto解析后的工作线程数: {perf.resolve_max_workers()}")
        
        print("✅ auto配置测试通过")
        return True
    except Exception as e:
        print(f"❌ auto配置测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    config_data = {
        'structure': {
            'body_rows': 5,
            'cols': 4,
            'header_rows': 1
        }
        # 没有performance配置
    }
    
    try:
        config = RenderConfig(**config_data)
        print(f"   - performance配置: {config.performance}")
        
        print("✅ 向后兼容性测试通过")
        return True
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def main():
    """运行验证测试"""
    print("🚀 步骤1验证：性能配置模型实现")
    print("="*50)
    
    tests = [
        test_basic_config,
        test_auto_config,
        test_backward_compatibility
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 步骤1验证成功！性能配置模型已正确实现。")
        return True
    else:
        print("❌ 步骤1验证失败，需要修复问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
