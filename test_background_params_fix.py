#!/usr/bin/env python3
"""
测试background_params修复

验证BackgroundComposer.compose()方法的background_params参数修复是否正确。
"""

import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

from table_render.config import RenderConfig
from table_render.main_generator import MainGenerator


def test_background_composer_method_signature():
    """测试BackgroundComposer方法签名"""
    print("=== 测试BackgroundComposer方法签名 ===")
    
    try:
        from table_render.postprocessors.background_composer import BackgroundComposer
        import inspect
        
        # 检查compose方法的签名
        compose_signature = inspect.signature(BackgroundComposer.compose)
        print(f"✅ BackgroundComposer.compose 签名: {compose_signature}")
        
        # 检查是否包含必要的参数
        params = compose_signature.parameters
        required_params = ['table_image', 'annotations', 'background_path']
        optional_params = ['max_scale_factor', 'transparency_config', 'background_params', 'sample_seed']
        
        for param in required_params:
            if param in params:
                print(f"✅ 必需参数 {param} 存在")
            else:
                print(f"❌ 必需参数 {param} 缺失")
                return False
        
        for param in optional_params:
            if param in params:
                print(f"✅ 可选参数 {param} 存在")
            else:
                print(f"⚠️  可选参数 {param} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ BackgroundComposer方法签名检查失败: {e}")
        return False


def test_image_augmentor_method_signature():
    """测试ImageAugmentor方法签名"""
    print("\n=== 测试ImageAugmentor方法签名 ===")
    
    try:
        from table_render.postprocessors.image_augmentor import ImageAugmentor
        import inspect
        
        # 创建ImageAugmentor实例
        augmentor = ImageAugmentor(42)
        
        # 检查_apply_background_composition方法的签名
        method_signature = inspect.signature(augmentor._apply_background_composition)
        print(f"✅ ImageAugmentor._apply_background_composition 签名: {method_signature}")
        
        # 检查是否包含background_params参数
        params = method_signature.parameters
        if 'background_params' in params:
            print("✅ background_params 参数存在")
        else:
            print("❌ background_params 参数缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ImageAugmentor方法签名检查失败: {e}")
        return False


def test_background_generation_with_margin_control():
    """测试带有margin_control的背景图生成"""
    print("\n=== 测试带有margin_control的背景图生成 ===")
    
    # 创建包含背景图和margin_control的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_background': True,
            'background_dirs': ['./sample_data'],  # 假设存在样本数据目录
            'background_dir_probabilities': [1.0],
            'margin_control': {
                'range_list': [[30, 60], [60, 100]],
                'probability_list': [0.7, 0.3]
            }
        },
        'performance': {
            'enable_parallel': False,  # 使用串行模式以便调试
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 带背景图的MainGenerator创建成功")
            
            # 测试单个样本生成
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成（包含背景图和margin_control）...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 图像大小: {result['image_size_bytes']} bytes")
            
            return True
            
        except NameError as e:
            if "background_params" in str(e):
                print(f"❌ 仍然存在background_params未定义错误: {e}")
                return False
            else:
                print(f"❌ 其他NameError: {e}")
                return False
        except Exception as e:
            print(f"❌ 背景图生成测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def test_background_generation_without_margin_control():
    """测试不带margin_control的背景图生成"""
    print("\n=== 测试不带margin_control的背景图生成 ===")
    
    # 创建只有背景图没有margin_control的测试配置
    config_data = {
        'structure': {
            'header_rows': 1,
            'body_rows': 2,
            'cols': 3,
            'merge_probability': 0.0,
            'max_row_span': 1,
            'max_col_span': 1
        },
        'postprocessing': {
            'apply_background': True,
            'background_dirs': ['./sample_data'],
            'background_dir_probabilities': [1.0]
            # 注意：没有margin_control配置
        },
        'performance': {
            'enable_parallel': False,
            'max_workers': 1,
            'max_browser_instances': 1
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_data['output'] = {'output_dir': temp_dir}
        
        try:
            config = RenderConfig(**config_data)
            generator = MainGenerator(config, debug_mode=False)
            
            print("✅ 不带margin_control的MainGenerator创建成功")
            
            # 测试单个样本生成
            from table_render.utils.file_utils import FileUtils
            output_dirs = FileUtils.ensure_output_dirs(temp_dir)
            
            print("开始测试单个样本生成（只有背景图，无margin_control）...")
            result = generator.generate_single_sample(
                sample_index=0,
                output_dirs=output_dirs,
                use_turbo_jpeg=False,
                turbo_format_hint="png"
            )
            
            print("✅ 单个样本生成成功")
            print(f"   - 样本索引: {result['sample_index']}")
            print(f"   - 生成时间: {result['duration_seconds']:.2f}秒")
            print(f"   - 图像大小: {result['image_size_bytes']} bytes")
            
            return True
            
        except NameError as e:
            if "background_params" in str(e):
                print(f"❌ 仍然存在background_params未定义错误: {e}")
                return False
            else:
                print(f"❌ 其他NameError: {e}")
                return False
        except Exception as e:
            print(f"❌ 背景图生成测试失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数"""
    print("TableRender V5.2 background_params修复测试")
    print("=" * 50)
    
    # 测试方法签名
    composer_signature_success = test_background_composer_method_signature()
    augmentor_signature_success = test_image_augmentor_method_signature()
    
    # 测试背景图生成（带margin_control）
    with_margin_success = test_background_generation_with_margin_control()
    
    # 测试背景图生成（不带margin_control）
    without_margin_success = test_background_generation_without_margin_control()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"BackgroundComposer签名: {'✅ 通过' if composer_signature_success else '❌ 失败'}")
    print(f"ImageAugmentor签名: {'✅ 通过' if augmentor_signature_success else '❌ 失败'}")
    print(f"带margin_control生成: {'✅ 通过' if with_margin_success else '❌ 失败'}")
    print(f"不带margin_control生成: {'✅ 通过' if without_margin_success else '❌ 失败'}")
    
    all_success = all([
        composer_signature_success,
        augmentor_signature_success,
        with_margin_success,
        without_margin_success
    ])
    
    if all_success:
        print("\n🎉 所有测试通过！background_params问题已修复")
        print("现在可以正常使用背景图合成功能")
        
        print("\n📋 修复总结:")
        print("1. ✅ BackgroundComposer.compose()方法添加了background_params参数")
        print("2. ✅ BackgroundComposer.compose()方法添加了sample_seed参数")
        print("3. ✅ ImageAugmentor._apply_background_composition()方法更新了参数")
        print("4. ✅ 参数传递链路修复完成")
        print("5. ✅ 支持带/不带margin_control的背景图生成")
        
    else:
        print("\n💥 部分测试失败，仍存在问题")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
